#!/usr/bin/env python3
"""
HTTPS сервер для Mini App с самоподписанным сертификатом
"""

from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS
import ssl
import os

app = Flask(__name__, static_folder='webapp', template_folder='webapp')
CORS(app)

@app.route('/')
def index():
    """Главная страница Mini App"""
    return send_from_directory('webapp', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """Статические файлы"""
    return send_from_directory('webapp', filename)

@app.route('/api/profiles', methods=['GET'])
def get_profiles():
    """Получение анкет для просмотра (мок-данные)"""
    mock_profiles = [
        {
            'id': 1,
            'name': 'Анна',
            'age': 25,
            'city': 'Москва',
            'description': 'Люблю путешествовать и читать книги. Ищу интересного собеседника для серьезных отношений.',
            'photo': None
        },
        {
            'id': 2,
            'name': 'Мария',
            'age': 23,
            'city': 'Санкт-Петербург',
            'description': 'Фотограф и художник. Обожаю кофе и долгие прогулки по городу.',
            'photo': None
        },
        {
            'id': 3,
            'name': 'Елена',
            'age': 27,
            'city': 'Екатеринбург',
            'description': 'Работаю в IT, увлекаюсь спортом и готовкой. Ищу того, с кем можно строить планы на будущее.',
            'photo': None
        }
    ]
    return jsonify({'profiles': mock_profiles})

@app.route('/api/reaction', methods=['POST'])
def send_reaction():
    """Отправка реакции (мок)"""
    data = request.get_json()
    reaction_type = data.get('reaction_type')
    
    # Симулируем случайное совпадение
    import random
    is_match = reaction_type == 'like' and random.random() < 0.3
    
    return jsonify({
        'success': True,
        'is_match': is_match,
        'match_user': {
            'id': 123,
            'name': 'Анна',
            'age': 25,
            'city': 'Москва'
        } if is_match else None
    })

@app.route('/api/matches', methods=['GET'])
def get_matches():
    """Получение совпадений (мок)"""
    mock_matches = [
        {'id': 1, 'name': 'Анна', 'age': 25, 'city': 'Москва', 'photo': None},
        {'id': 2, 'name': 'Мария', 'age': 23, 'city': 'СПб', 'photo': None}
    ]
    return jsonify({'matches': mock_matches})

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Получение статистики (мок)"""
    return jsonify({
        'likes_sent': 15,
        'likes_received': 8,
        'matches_count': 3,
        'dislikes_sent': 12
    })

@app.route('/api/user', methods=['GET'])
def get_user_profile():
    """Получение профиля пользователя (мок)"""
    return jsonify({
        'id': 123,
        'name': 'Пользователь',
        'age': 25,
        'city': 'Москва',
        'description': 'Расскажите о себе...',
        'photo': None
    })

@app.route('/api/profile', methods=['PUT'])
def update_profile():
    """Обновление профиля (мок)"""
    return jsonify({'success': True})

def create_self_signed_cert():
    """Создание самоподписанного сертификата"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import datetime
        
        # Генерируем приватный ключ
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # Создаем сертификат
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "RU"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Moscow"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Moscow"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Dating Bot"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.IPAddress("127.0.0.1"),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # Сохраняем сертификат и ключ
        with open("cert.pem", "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        with open("key.pem", "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        print("✅ Самоподписанный сертификат создан!")
        return True
        
    except ImportError:
        print("❌ Для создания сертификата нужна библиотека cryptography")
        print("Установите: pip install cryptography")
        return False
    except Exception as e:
        print(f"❌ Ошибка создания сертификата: {e}")
        return False

if __name__ == '__main__':
    print("🔐 Создание HTTPS сервера для Mini App...")
    
    # Проверяем наличие сертификата
    if not (os.path.exists('cert.pem') and os.path.exists('key.pem')):
        print("📜 Создание самоподписанного сертификата...")
        if not create_self_signed_cert():
            print("❌ Не удалось создать сертификат. Запускаем HTTP сервер...")
            app.run(host='0.0.0.0', port=5000, debug=False)
            exit()
    
    # Создаем SSL контекст
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
    context.load_cert_chain('cert.pem', 'key.pem')
    
    print("🌐 HTTPS сервер запущен!")
    print("📱 Mini App доступен по адресу: https://localhost:5000")
    print("⚠️  Браузер покажет предупреждение о безопасности - это нормально для самоподписанного сертификата")
    
    app.run(host='0.0.0.0', port=5000, debug=False, ssl_context=context)
