#!/usr/bin/env python3
"""
Скрипт для быстрой настройки GitHub Actions деплоя
"""

import os
import webbrowser
import time

def print_header():
    print("🚀 НАСТРОЙКА GITHUB ACTIONS ДЛЯ АВТОМАТИЧЕСКОГО ДЕПЛОЯ")
    print("=" * 60)

def print_step(step_num, title):
    print(f"\n📋 ШАГ {step_num}: {title}")
    print("-" * 40)

def open_urls():
    """Открывает необходимые URL для настройки"""
    urls = [
        ("GitHub - создание репозитория", "https://github.com/new"),
        ("Netlify - токены доступа", "https://app.netlify.com/user/applications"),
        ("Netlify - ваши сайты", "https://app.netlify.com/teams/personal/sites")
    ]
    
    print("🌐 Открываю необходимые страницы...")
    for name, url in urls:
        print(f"   📖 {name}")
        webbrowser.open(url)
        time.sleep(1)

def show_file_structure():
    """Показывает структуру файлов для загрузки"""
    print("📁 Файлы для загрузки в GitHub репозиторий:")
    print("   ├── index.html (основное приложение)")
    print("   ├── .github/")
    print("   │   └── workflows/")
    print("   │       └── deploy.yml (GitHub Actions)")
    print("   ├── package.json (конфигурация)")
    print("   ├── README.md (документация)")
    print("   └── .gitignore (игнорируемые файлы)")

def show_secrets_setup():
    """Показывает инструкции по настройке секретов"""
    print("🔐 Настройка GitHub Secrets:")
    print("   1. Репозиторий → Settings → Secrets and variables → Actions")
    print("   2. New repository secret")
    print("   3. Добавьте два секрета:")
    print("      • NETLIFY_AUTH_TOKEN (из Netlify User Applications)")
    print("      • NETLIFY_SITE_ID (из настроек вашего сайта)")

def show_bot_update():
    """Показывает как обновить URL в боте"""
    print("🤖 Обновление URL в боте:")
    print('   В файле main.py замените URL на:')
    print('   web_app=types.WebAppInfo(url="https://ВАШ_SITE_ID.netlify.app")')

def main():
    print_header()
    
    # Проверяем наличие файлов
    if not os.path.exists('github-deploy'):
        print("❌ Папка github-deploy не найдена!")
        print("   Сначала запустите основной скрипт для создания файлов")
        return
    
    print("✅ Файлы GitHub Actions готовы!")
    show_file_structure()
    
    print("\n🎯 ПЛАН ДЕЙСТВИЙ:")
    print("1. Создать GitHub репозиторий")
    print("2. Загрузить файлы")
    print("3. Получить Netlify токены")
    print("4. Настроить GitHub Secrets")
    print("5. Активировать Actions")
    print("6. Обновить URL в боте")
    
    choice = input("\n❓ Открыть необходимые страницы? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        open_urls()
        
        print_step(1, "СОЗДАНИЕ РЕПОЗИТОРИЯ")
        print("   • Название: dating-bot-miniapp")
        print("   • Описание: Mini App для Telegram бота знакомств")
        print("   • Публичный репозиторий")
        print("   • Инициализировать с README")
        
        input("\n⏳ Создайте репозиторий и нажмите Enter...")
        
        print_step(2, "ЗАГРУЗКА ФАЙЛОВ")
        print("   • Загрузите все файлы из папки github-deploy/")
        print("   • Убедитесь, что структура папок сохранена")
        print("   • Особенно важно: .github/workflows/deploy.yml")
        
        input("\n⏳ Загрузите файлы и нажмите Enter...")
        
        print_step(3, "ПОЛУЧЕНИЕ NETLIFY ТОКЕНОВ")
        print("   🔑 NETLIFY_AUTH_TOKEN:")
        print("      • Netlify → User Applications → New access token")
        print("      • Название: GitHub Actions Deploy")
        print("      • Скопируйте токен")
        print()
        print("   🆔 NETLIFY_SITE_ID:")
        print("      • Ваш сайт → Site settings → Site ID")
        print(f"      • Например: fluffy-crumble-05e0fb")
        
        input("\n⏳ Получите токены и нажмите Enter...")
        
        print_step(4, "НАСТРОЙКА GITHUB SECRETS")
        show_secrets_setup()
        
        input("\n⏳ Настройте секреты и нажмите Enter...")
        
        print_step(5, "АКТИВАЦИЯ ACTIONS")
        print("   • Репозиторий → Actions")
        print("   • Enable workflows (если нужно)")
        print("   • Workflow должен появиться автоматически")
        
        input("\n⏳ Активируйте Actions и нажмите Enter...")
        
        print_step(6, "ОБНОВЛЕНИЕ БОТА")
        show_bot_update()
        
        print("\n🎉 НАСТРОЙКА ЗАВЕРШЕНА!")
        print("=" * 60)
        print("✅ Теперь при каждом push в main ветку:")
        print("   • Mini App автоматически обновляется")
        print("   • Netlify получает новую версию")
        print("   • Telegram бот использует актуальную версию")
        print()
        print("🧪 ТЕСТИРОВАНИЕ:")
        print("   1. Внесите изменение в index.html")
        print("   2. Сделайте commit и push")
        print("   3. Проверьте Actions → Deploy workflow")
        print("   4. Убедитесь, что сайт обновился")
        print("   5. Протестируйте в Telegram боте")
        
    else:
        print("\n📋 Следуйте инструкциям в файле:")
        print("   github-deploy/SETUP_GITHUB_ACTIONS.md")
    
    print("\n🔗 Полезные ссылки:")
    print("   📖 Документация: github-deploy/README.md")
    print("   ⚙️ Настройка: github-deploy/SETUP_GITHUB_ACTIONS.md")
    print("   📁 Файлы: папка github-deploy/")

if __name__ == "__main__":
    main()
