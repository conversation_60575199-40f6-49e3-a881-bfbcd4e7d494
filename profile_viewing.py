from telebot import types
from database import get_users_for_viewing, add_reaction, get_user

# Хранилище текущих просматриваемых анкет
viewing_sessions = {}

def show_profiles(bot, message, user_states):
    """Начать просмотр анкет"""
    user_id = message.from_user.id if hasattr(message, 'from_user') else message.chat.id
    chat_id = message.chat.id
    
    # Получаем пользователей для просмотра
    users = get_users_for_viewing(user_id, limit=10)
    
    if not users:
        no_profiles_text = """
😔 К сожалению, сейчас нет новых анкет для просмотра.

Попробуйте позже или пригласите друзей в бот!
        """
        
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
        
        bot.send_message(chat_id, no_profiles_text, reply_markup=markup)
        return
    
    # Сохраняем сессию просмотра
    viewing_sessions[user_id] = {
        'profiles': users,
        'current_index': 0
    }
    
    user_states[user_id] = "viewing_profiles"
    show_current_profile(bot, chat_id, user_id)

def show_current_profile(bot, chat_id, user_id):
    """Показать текущую анкету"""
    if user_id not in viewing_sessions:
        return
    
    session = viewing_sessions[user_id]
    profiles = session['profiles']
    current_index = session['current_index']
    
    if current_index >= len(profiles):
        # Закончились анкеты
        end_viewing(bot, chat_id, user_id)
        return
    
    current_profile = profiles[current_index]
    
    # Формируем текст анкеты
    profile_text = f"""
👤 Анкета #{current_index + 1} из {len(profiles)}

📝 Имя: {current_profile[2]}
🎂 Возраст: {current_profile[3]}
🏙️ Город: {current_profile[4]}
📖 О себе: {current_profile[5]}
    """
    
    # Создаем клавиатуру с кнопками
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👎 Не нравится", callback_data=f"dislike_{current_profile[0]}"),
        types.InlineKeyboardButton("❤️ Нравится", callback_data=f"like_{current_profile[0]}")
    )
    markup.add(types.InlineKeyboardButton("⏭️ Пропустить", callback_data=f"skip_{current_profile[0]}"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    # Отправляем анкету
    if current_profile[6]:  # Если есть фото
        try:
            bot.send_photo(chat_id, current_profile[6], caption=profile_text, reply_markup=markup)
        except Exception as e:
            print(f"Ошибка при отправке фото: {e}")
            bot.send_message(chat_id, profile_text, reply_markup=markup)
    else:
        bot.send_message(chat_id, profile_text, reply_markup=markup)

def handle_like_dislike(bot, call, user_states):
    """Обработка лайков и дизлайков"""
    user_id = call.from_user.id
    chat_id = call.message.chat.id
    
    # Парсим callback data
    action, target_user_id = call.data.split('_', 1)
    target_user_id = int(target_user_id)
    
    if action == "skip":
        # Просто переходим к следующей анкете
        next_profile(bot, chat_id, user_id)
        return
    
    # Добавляем реакцию в базу данных
    is_match = add_reaction(user_id, target_user_id, action)
    
    if is_match and action == "like":
        # Взаимный лайк!
        target_user = get_user(target_user_id)
        current_user = get_user(user_id)
        
        match_text = f"""
🎉 У вас взаимная симпатия!

❤️ {target_user[2]} тоже поставил(а) вам лайк!

Теперь вы можете начать общение. Вот контакт:
@{target_user[1] if target_user[1] else 'пользователь скрыл username'}
        """
        
        bot.send_message(chat_id, match_text)
        
        # Уведомляем второго пользователя
        match_text_for_target = f"""
🎉 У вас взаимная симпатия!

❤️ {current_user[2]} тоже поставил(а) вам лайк!

Теперь вы можете начать общение. Вот контакт:
@{current_user[1] if current_user[1] else 'пользователь скрыл username'}
        """
        
        try:
            bot.send_message(target_user_id, match_text_for_target)
        except:
            print(f"Не удалось отправить уведомление пользователю {target_user_id}")
    
    elif action == "like":
        bot.answer_callback_query(call.id, "❤️ Лайк отправлен!")
    else:
        bot.answer_callback_query(call.id, "👎 Анкета пропущена")
    
    # Переходим к следующей анкете
    next_profile(bot, chat_id, user_id)

def next_profile(bot, chat_id, user_id):
    """Перейти к следующей анкете"""
    if user_id not in viewing_sessions:
        return
    
    session = viewing_sessions[user_id]
    session['current_index'] += 1
    
    show_current_profile(bot, chat_id, user_id)

def end_viewing(bot, chat_id, user_id):
    """Завершить просмотр анкет"""
    end_text = """
✅ Вы просмотрели все доступные анкеты!

Заходите позже - возможно, появятся новые пользователи.
    """
    
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔄 Посмотреть еще", callback_data="view_profiles"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    bot.send_message(chat_id, end_text, reply_markup=markup)
    
    # Очищаем сессию
    if user_id in viewing_sessions:
        del viewing_sessions[user_id]
    
    if user_id in user_states:
        del user_states[user_id]
