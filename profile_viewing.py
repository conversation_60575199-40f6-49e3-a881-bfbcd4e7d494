from telebot import types
from database import get_users_for_viewing, add_reaction, get_user

# Хранилище текущих просматриваемых анкет
viewing_sessions = {}

def show_profiles(bot, message, user_states):
    """Начать просмотр анкет"""
    user_id = message.from_user.id if hasattr(message, 'from_user') else message.chat.id
    chat_id = message.chat.id

    # Очищаем старую сессию если есть
    if user_id in viewing_sessions:
        del viewing_sessions[user_id]

    # Удаляем пользователя из очереди уведомлений (он начал просмотр)
    remove_from_notification_queue(user_id)

    # Получаем пользователей для просмотра
    users = get_users_for_viewing(user_id, limit=10)

    # Дополнительная фильтрация собственной анкеты
    users = [user for user in users if user[0] != user_id]
    
    if not users:
        no_profiles_text = """
😔 К сожалению, сейчас нет новых анкет для просмотра.

Попробуйте позже или пригласите друзей в бот!
        """
        
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
        
        bot.send_message(chat_id, no_profiles_text, reply_markup=markup)
        return
    
    # Сохраняем сессию просмотра
    viewing_sessions[user_id] = {
        'profiles': users,
        'current_index': 0
    }
    
    user_states[user_id] = "viewing_profiles"
    show_current_profile(bot, chat_id, user_id)

def show_current_profile(bot, chat_id, user_id):
    """Показать текущую анкету"""
    if user_id not in viewing_sessions:
        print(f"Пользователь {user_id} не найден в viewing_sessions")
        return

    session = viewing_sessions[user_id]
    profiles = session['profiles']
    current_index = session['current_index']

    print(f"Показ анкеты для пользователя {user_id}: индекс {current_index} из {len(profiles)}")

    if current_index >= len(profiles):
        # Закончились анкеты
        print(f"Анкеты закончились для пользователя {user_id}")
        end_viewing(bot, chat_id, user_id)
        return
    
    current_profile = profiles[current_index]

    # Проверяем, что это не собственная анкета
    if current_profile[0] == user_id:
        print(f"Пропускаем собственную анкету пользователя {user_id}")
        session['current_index'] += 1
        show_current_profile(bot, chat_id, user_id)
        return

    # Формируем текст анкеты
    profile_text = f"""
👤 Анкета #{current_index + 1} из {len(profiles)}

📝 Имя: {current_profile[2]}
🎂 Возраст: {current_profile[3]}
🏙️ Город: {current_profile[4]}
📖 О себе: {current_profile[5]}
    """
    
    # Создаем клавиатуру с кнопками
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👎 Не нравится", callback_data=f"dislike_{current_profile[0]}"),
        types.InlineKeyboardButton("❤️ Нравится", callback_data=f"like_{current_profile[0]}")
    )
    markup.add(types.InlineKeyboardButton("⏭️ Пропустить", callback_data=f"skip_{current_profile[0]}"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    # Отправляем анкету
    if current_profile[6]:  # Если есть фото
        try:
            bot.send_photo(chat_id, current_profile[6], caption=profile_text, reply_markup=markup)
        except Exception as e:
            print(f"Ошибка при отправке фото: {e}")
            bot.send_message(chat_id, profile_text, reply_markup=markup)
    else:
        bot.send_message(chat_id, profile_text, reply_markup=markup)

def handle_like_dislike(bot, call, user_states):
    """Обработка лайков и дизлайков"""
    user_id = call.from_user.id
    chat_id = call.message.chat.id

    # Парсим callback data
    action, target_user_id = call.data.split('_', 1)

    # Проверяем, что это не skip_photo
    if action == "skip" and target_user_id == "photo":
        bot.answer_callback_query(call.id, "Используйте кнопку 'Пропустить фото' в процессе регистрации")
        return

    try:
        target_user_id = int(target_user_id)
    except ValueError:
        bot.answer_callback_query(call.id, "Ошибка обработки действия")
        return

    # Проверяем, что пользователь не лайкает сам себя
    if target_user_id == user_id:
        bot.answer_callback_query(call.id, "❌ Нельзя оценивать свою анкету!")
        return
    
    if action == "skip":
        # Просто переходим к следующей анкете
        print(f"Пользователь {user_id} пропустил анкету {target_user_id}")
        try:
            bot.answer_callback_query(call.id, "⏭️ Анкета пропущена")
        except:
            pass
        next_profile(bot, chat_id, user_id)
        return
    
    # Добавляем реакцию в базу данных
    print(f"Пользователь {user_id} поставил {action} пользователю {target_user_id}")
    is_match = add_reaction(user_id, target_user_id, action)
    
    if is_match and action == "like":
        # Взаимный лайк!
        target_user = get_user(target_user_id)
        current_user = get_user(user_id)
        
        match_text = f"""
🎉 У вас взаимная симпатия!

❤️ {target_user[2]} тоже поставил(а) вам лайк!

Теперь вы можете начать общение. Вот контакт:
@{target_user[1] if target_user[1] else 'пользователь скрыл username'}
        """
        
        bot.send_message(chat_id, match_text)
        
        # Уведомляем второго пользователя
        match_text_for_target = f"""
🎉 У вас взаимная симпатия!

❤️ {current_user[2]} тоже поставил(а) вам лайк!

Теперь вы можете начать общение. Вот контакт:
@{current_user[1] if current_user[1] else 'пользователь скрыл username'}
        """
        
        try:
            bot.send_message(target_user_id, match_text_for_target)
        except:
            print(f"Не удалось отправить уведомление пользователю {target_user_id}")
    
    elif action == "like":
        try:
            bot.answer_callback_query(call.id, "❤️ Лайк отправлен!")
        except:
            pass
    else:
        try:
            bot.answer_callback_query(call.id, "👎 Анкета пропущена")
        except:
            pass
    
    # Переходим к следующей анкете
    next_profile(bot, chat_id, user_id)

def next_profile(bot, chat_id, user_id):
    """Перейти к следующей анкете"""
    if user_id not in viewing_sessions:
        return

    session = viewing_sessions[user_id]
    session['current_index'] += 1

    # Проверяем, есть ли еще анкеты
    if session['current_index'] >= len(session['profiles']):
        # Пытаемся загрузить новые анкеты
        new_profiles = get_users_for_viewing(user_id, limit=10)
        # Дополнительная фильтрация собственной анкеты
        new_profiles = [user for user in new_profiles if user[0] != user_id]

        if new_profiles:
            # Добавляем новые анкеты к текущей сессии
            session['profiles'].extend(new_profiles)
            show_current_profile(bot, chat_id, user_id)
        else:
            # Анкеты закончились
            end_viewing(bot, chat_id, user_id)
    else:
        show_current_profile(bot, chat_id, user_id)

def end_viewing(bot, chat_id, user_id):
    """Завершить просмотр анкет"""
    end_text = """
✅ Вы просмотрели все доступные анкеты!

🔔 Мы уведомим вас, когда появятся новые пользователи для знакомства.

💡 Пока можете:
• Посмотреть свою статистику
• Редактировать анкету
• Проверить взаимные симпатии
    """

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔄 Проверить новые анкеты", callback_data="view_profiles"))
    markup.add(types.InlineKeyboardButton("📊 Моя статистика", callback_data="show_stats"))
    markup.add(types.InlineKeyboardButton("❤️ Мои лайки", callback_data="my_likes"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))

    bot.send_message(chat_id, end_text, reply_markup=markup)

    # Добавляем пользователя в список ожидания уведомлений
    add_to_notification_queue(user_id)
    
    # Очищаем сессию
    if user_id in viewing_sessions:
        del viewing_sessions[user_id]

# Система уведомлений о новых анкетах
notification_queue = set()

def add_to_notification_queue(user_id):
    """Добавить пользователя в очередь уведомлений"""
    notification_queue.add(user_id)
    print(f"Пользователь {user_id} добавлен в очередь уведомлений")

def remove_from_notification_queue(user_id):
    """Удалить пользователя из очереди уведомлений"""
    notification_queue.discard(user_id)

def notify_about_new_profiles(bot, new_user_id):
    """Уведомить пользователей о новой анкете"""
    if not notification_queue:
        return

    # Получаем информацию о новом пользователе
    from database import get_user
    new_user = get_user(new_user_id)
    if not new_user:
        return

    notification_text = f"""
🎉 Новая анкета для знакомства!

Появился новый пользователь: {new_user[2]}, {new_user[3]} лет из города {new_user[4]}

Хотите посмотреть новые анкеты?
    """

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("👀 Посмотреть анкеты", callback_data="view_profiles"))
    markup.add(types.InlineKeyboardButton("❌ Не сейчас", callback_data="dismiss_notification"))

    # Отправляем уведомления всем в очереди
    users_to_remove = []
    for user_id in notification_queue.copy():
        try:
            bot.send_message(user_id, notification_text, reply_markup=markup)
            users_to_remove.append(user_id)
        except Exception as e:
            print(f"Не удалось отправить уведомление пользователю {user_id}: {e}")
            users_to_remove.append(user_id)

    # Удаляем пользователей из очереди после отправки уведомления
    for user_id in users_to_remove:
        notification_queue.discard(user_id)
