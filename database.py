import sqlite3
from config import DATABASE_NAME
import datetime

def get_connection():
    """Получить соединение с базой данных"""
    return sqlite3.connect(DATABASE_NAME)

def init_database():
    """Инициализация базы данных"""
    conn = get_connection()
    cursor = conn.cursor()
    
    # Таблица пользователей
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            name TEXT NOT NULL,
            age INTEGER NOT NULL,
            city TEXT NOT NULL,
            description TEXT,
            photo_id TEXT,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    ''')
    
    # Таблица лайков/дизлайков
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS reactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            from_user_id INTEGER NOT NULL,
            to_user_id INTEGER NOT NULL,
            reaction_type TEXT NOT NULL CHECK (reaction_type IN ('like', 'dislike')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (from_user_id) REFERENCES users (user_id),
            FOREIGN KEY (to_user_id) REFERENCES users (user_id),
            UNIQUE(from_user_id, to_user_id)
        )
    ''')
    
    # Таблица совпадений (взаимных лайков)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS matches (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user1_id INTEGER NOT NULL,
            user2_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user1_id) REFERENCES users (user_id),
            FOREIGN KEY (user2_id) REFERENCES users (user_id),
            UNIQUE(user1_id, user2_id)
        )
    ''')
    
    conn.commit()
    conn.close()

def create_user(user_id, username, name, age, city, description, photo_id=None):
    """Создать нового пользователя"""
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO users (user_id, username, name, age, city, description, photo_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, username, name, age, city, description, photo_id))
        
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False
    finally:
        conn.close()

def get_user(user_id):
    """Получить пользователя по ID"""
    conn = get_connection()
    cursor = conn.cursor()
    
    cursor.execute('SELECT * FROM users WHERE user_id = ?', (user_id,))
    user = cursor.fetchone()
    
    conn.close()
    return user

def update_user_field(user_id, field, value):
    """Обновить поле пользователя"""
    conn = get_connection()
    cursor = conn.cursor()
    
    query = f'UPDATE users SET {field} = ? WHERE user_id = ?'
    cursor.execute(query, (value, user_id))
    
    conn.commit()
    conn.close()

def get_users_for_viewing(current_user_id, limit=10, min_age=None, max_age=None, city=None):
    """Получить пользователей для просмотра (исключая уже оцененных)"""
    conn = get_connection()
    cursor = conn.cursor()

    # Базовый запрос
    query = '''
        SELECT * FROM users
        WHERE user_id != ?
        AND is_active = 1
        AND user_id NOT IN (
            SELECT to_user_id FROM reactions WHERE from_user_id = ?
        )
    '''
    params = [current_user_id, current_user_id]

    # Добавляем фильтры
    if min_age is not None:
        query += ' AND age >= ?'
        params.append(min_age)

    if max_age is not None:
        query += ' AND age <= ?'
        params.append(max_age)

    if city is not None:
        query += ' AND LOWER(city) LIKE LOWER(?)'
        params.append(f'%{city}%')

    query += ' ORDER BY RANDOM() LIMIT ?'
    params.append(limit)

    cursor.execute(query, params)
    users = cursor.fetchall()
    conn.close()
    return users

def add_reaction(from_user_id, to_user_id, reaction_type):
    """Добавить реакцию (лайк/дизлайк)"""
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT OR REPLACE INTO reactions (from_user_id, to_user_id, reaction_type)
            VALUES (?, ?, ?)
        ''', (from_user_id, to_user_id, reaction_type))
        
        conn.commit()
        
        # Проверяем, есть ли взаимный лайк
        if reaction_type == 'like':
            cursor.execute('''
                SELECT * FROM reactions 
                WHERE from_user_id = ? AND to_user_id = ? AND reaction_type = 'like'
            ''', (to_user_id, from_user_id))
            
            mutual_like = cursor.fetchone()
            
            if mutual_like:
                # Создаем совпадение
                create_match(from_user_id, to_user_id)
                conn.close()
                return True  # Взаимный лайк
        
        conn.close()
        return False  # Обычная реакция
        
    except sqlite3.Error as e:
        print(f"Ошибка при добавлении реакции: {e}")
        conn.close()
        return False

def create_match(user1_id, user2_id):
    """Создать совпадение между пользователями"""
    conn = get_connection()
    cursor = conn.cursor()
    
    # Убеждаемся, что user1_id < user2_id для избежания дублирования
    if user1_id > user2_id:
        user1_id, user2_id = user2_id, user1_id
    
    try:
        cursor.execute('''
            INSERT OR IGNORE INTO matches (user1_id, user2_id)
            VALUES (?, ?)
        ''', (user1_id, user2_id))
        
        conn.commit()
    except sqlite3.Error as e:
        print(f"Ошибка при создании совпадения: {e}")
    finally:
        conn.close()

def get_user_matches(user_id):
    """Получить совпадения пользователя"""
    conn = get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT u.* FROM users u
        JOIN matches m ON (
            (m.user1_id = ? AND m.user2_id = u.user_id) OR
            (m.user2_id = ? AND m.user1_id = u.user_id)
        )
        WHERE u.user_id != ?
        ORDER BY m.created_at DESC
    ''', (user_id, user_id, user_id))
    
    matches = cursor.fetchall()
    conn.close()
    return matches

def get_user_likes_count(user_id):
    """Получить количество лайков пользователя"""
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT COUNT(*) FROM reactions
        WHERE to_user_id = ? AND reaction_type = 'like'
    ''', (user_id,))

    count = cursor.fetchone()[0]
    conn.close()
    return count

def get_bot_statistics():
    """Получить общую статистику бота"""
    conn = get_connection()
    cursor = conn.cursor()

    # Общее количество пользователей
    cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
    total_users = cursor.fetchone()[0]

    # Количество лайков
    cursor.execute('SELECT COUNT(*) FROM reactions WHERE reaction_type = "like"')
    total_likes = cursor.fetchone()[0]

    # Количество совпадений
    cursor.execute('SELECT COUNT(*) FROM matches')
    total_matches = cursor.fetchone()[0]

    # Новые пользователи за последние 24 часа
    cursor.execute('''
        SELECT COUNT(*) FROM users
        WHERE registration_date >= datetime('now', '-1 day')
        AND is_active = 1
    ''')
    new_users_24h = cursor.fetchone()[0]

    conn.close()

    return {
        'total_users': total_users,
        'total_likes': total_likes,
        'total_matches': total_matches,
        'new_users_24h': new_users_24h
    }

def get_user_activity_stats(user_id):
    """Получить детальную статистику пользователя"""
    conn = get_connection()
    cursor = conn.cursor()

    # Лайки отправленные
    cursor.execute('''
        SELECT COUNT(*) FROM reactions
        WHERE from_user_id = ? AND reaction_type = 'like'
    ''', (user_id,))
    likes_sent = cursor.fetchone()[0]

    # Лайки полученные
    cursor.execute('''
        SELECT COUNT(*) FROM reactions
        WHERE to_user_id = ? AND reaction_type = 'like'
    ''', (user_id,))
    likes_received = cursor.fetchone()[0]

    # Дизлайки отправленные
    cursor.execute('''
        SELECT COUNT(*) FROM reactions
        WHERE from_user_id = ? AND reaction_type = 'dislike'
    ''', (user_id,))
    dislikes_sent = cursor.fetchone()[0]

    # Совпадения
    cursor.execute('''
        SELECT COUNT(*) FROM matches
        WHERE user1_id = ? OR user2_id = ?
    ''', (user_id, user_id))
    matches_count = cursor.fetchone()[0]

    conn.close()

    return {
        'likes_sent': likes_sent,
        'likes_received': likes_received,
        'dislikes_sent': dislikes_sent,
        'matches_count': matches_count
    }
