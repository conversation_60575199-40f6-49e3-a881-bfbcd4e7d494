
🚀 ИНСТРУКЦИИ ПО ДЕПЛОЮ MINI APP

📋 Вариант 1: GitHub Pages (Рекомендуется)
===========================================

1. Создайте новый репозиторий на GitHub:
   - Название: dating-bot-miniapp
   - Публичный репозиторий
   - Инициализируйте с README

2. Загрузите файлы:
   - Скопируйте файл deploy/index.html в корень репозитория
   - Переименуйте его просто в index.html

3. Включите GitHub Pages:
   - Settings → Pages
   - Source: Deploy from a branch
   - Branch: main
   - Folder: / (root)
   - Save

4. Получите URL:
   - Ваш URL будет: https://USERNAME.github.io/dating-bot-miniapp
   - Замените USERNAME на ваш GitHub логин

5. Обновите бота:
   - Откройте main.py
   - Найдите строку с web_app=types.WebAppInfo(url="...")
   - Замените URL на ваш GitHub Pages URL

6. Перезапустите бота:
   - python main.py

📋 Вариант 2: Netlify Drop (Быстрый)
====================================

1. Откройте: https://app.netlify.com/drop
2. Перетащите папку 'deploy' в область загрузки
3. Получите URL (например: https://amazing-app-123456.netlify.app)
4. Обновите URL в main.py
5. Перезапустите бота

📋 Вариант 3: Vercel (Альтернатива)
===================================

1. Откройте: https://vercel.com
2. Войдите через GitHub
3. Import Project → выберите репозиторий
4. Deploy
5. Получите URL

⚠️ ВАЖНО:
- URL должен быть HTTPS
- Проверьте доступность в браузере
- Telegram не принимает localhost URLs

🎯 ТЕСТИРОВАНИЕ:
1. Откройте полученный URL в браузере
2. Проверьте работу Mini App
3. Откройте бота в Telegram
4. Нажмите "📱 Открыть Mini App"
5. Наслаждайтесь!

📁 Готовые файлы находятся в папке 'deploy/'
