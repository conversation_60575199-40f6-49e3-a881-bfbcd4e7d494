#!/usr/bin/env python3
"""
Тестовый скрипт для проверки функций бота
"""

import sqlite3
from database import get_connection, get_users_for_viewing, add_reaction

def test_database():
    """Тест базы данных"""
    print("🧪 Тестирование базы данных...")
    
    # Проверяем подключение
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✅ Подключение к БД: OK. Пользователей: {user_count}")
        conn.close()
    except Exception as e:
        print(f"❌ Ошибка подключения к БД: {e}")
        return False
    
    return True

def test_self_exclusion():
    """Тест исключения собственной анкеты"""
    print("\n🧪 Тестирование исключения собственной анкеты...")
    
    # Получаем всех пользователей
    conn = get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT user_id FROM users LIMIT 5")
    users = cursor.fetchall()
    conn.close()
    
    if not users:
        print("⚠️ Нет пользователей для тестирования")
        return True
    
    for user in users:
        user_id = user[0]
        profiles = get_users_for_viewing(user_id, limit=10)
        
        # Проверяем, что собственная анкета не включена
        self_in_results = any(profile[0] == user_id for profile in profiles)
        
        if self_in_results:
            print(f"❌ Пользователь {user_id} видит свою анкету!")
            return False
        else:
            print(f"✅ Пользователь {user_id}: собственная анкета исключена")
    
    return True

def test_self_reaction():
    """Тест защиты от самолайков"""
    print("\n🧪 Тестирование защиты от самолайков...")
    
    # Пытаемся добавить самолайк
    test_user_id = 999999  # Тестовый ID
    result = add_reaction(test_user_id, test_user_id, 'like')
    
    if result:
        print("❌ Самолайк был разрешен!")
        return False
    else:
        print("✅ Самолайк заблокирован")
        return True

def test_reactions_table():
    """Тест таблицы реакций"""
    print("\n🧪 Тестирование таблицы реакций...")
    
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Проверяем структуру таблицы
        cursor.execute("PRAGMA table_info(reactions)")
        columns = cursor.fetchall()
        
        expected_columns = ['id', 'from_user_id', 'to_user_id', 'reaction_type', 'created_at']
        actual_columns = [col[1] for col in columns]
        
        for col in expected_columns:
            if col in actual_columns:
                print(f"✅ Колонка '{col}': найдена")
            else:
                print(f"❌ Колонка '{col}': отсутствует")
                return False
        
        # Проверяем количество реакций
        cursor.execute("SELECT COUNT(*) FROM reactions")
        reaction_count = cursor.fetchone()[0]
        print(f"📊 Всего реакций в БД: {reaction_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке таблицы реакций: {e}")
        return False

def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестов бота знакомств\n")
    
    tests = [
        ("База данных", test_database),
        ("Исключение собственной анкеты", test_self_exclusion),
        ("Защита от самолайков", test_self_reaction),
        ("Таблица реакций", test_reactions_table),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: ПРОЙДЕН\n")
            else:
                print(f"❌ {test_name}: ПРОВАЛЕН\n")
        except Exception as e:
            print(f"💥 {test_name}: ОШИБКА - {e}\n")
    
    print("=" * 50)
    print(f"📊 Результаты тестирования: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 Все тесты пройдены успешно!")
    else:
        print("⚠️ Некоторые тесты провалены. Требуется исправление.")
    
    return passed == total

if __name__ == "__main__":
    main()
