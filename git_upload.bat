@echo off
echo 🚀 Загрузка workflow файла через Git
echo =====================================

echo 📁 Проверка Git репозитория...
if not exist ".git" (
    echo ❌ Git репозиторий не инициализирован
    echo 🔧 Инициализирую Git...
    git init
    git remote add origin https://github.com/KampeLoL/znakomstva_bot.git
)

echo 📦 Добавление файлов...
git add .github/workflows/deploy-miniapp.yml
git add deploy/index.html
git add main.py

echo 💾 Создание commit...
git commit -m "Add GitHub Actions workflow for Mini App auto-deploy"

echo 🚀 Загрузка в GitHub...
git push origin main

echo ✅ Готово! Проверьте GitHub Actions
pause
