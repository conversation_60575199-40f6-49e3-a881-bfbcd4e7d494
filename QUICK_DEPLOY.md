# ⚡ Быстрый деплой Mini App

## 🚀 Метод 1: GitHub Pages (5 минут)

### Шаги:
1. **Создайте новый репозиторий на GitHub:**
   - Название: `dating-bot-miniapp`
   - Публичный репозиторий

2. **Загрузите файлы:**
   - Скопируйте все файлы из папки `webapp/` в корень репозитория
   - Файлы: `index.html`, `styles.css`, `app_standalone.js`

3. **Включите GitHub Pages:**
   - Settings → Pages
   - Source: Deploy from a branch
   - Branch: main
   - Folder: / (root)

4. **Получите URL:**
   - Ваш URL: `https://username.github.io/dating-bot-miniapp`

5. **Обновите бота:**
   ```python
   # В main.py замените URL:
   markup.add(types.InlineKeyboardButton(
       "📱 Открыть Mini App", 
       web_app=types.WebAppInfo(url="https://username.github.io/dating-bot-miniapp")
   ))
   ```

## 🌐 Метод 2: Netlify Drop (2 минуты)

### Шаги:
1. Откройте https://app.netlify.com/drop
2. Перетащите папку `webapp` в область загрузки
3. Получите URL (например: `https://amazing-app-123456.netlify.app`)
4. Обновите URL в боте

## 🔧 Метод 3: Vercel (3 минуты)

### Шаги:
1. Откройте https://vercel.com
2. Войдите через GitHub
3. Import Project → выберите репозиторий
4. Deploy
5. Получите URL

## 📱 Тестирование

После деплоя:
1. Откройте URL в браузере
2. Проверьте, что Mini App загружается
3. Протестируйте свайп-жесты
4. Запустите бота: `python main.py`
5. Откройте бота в Telegram
6. Нажмите "📱 Открыть Mini App"

## 🎯 Готовые файлы для деплоя

В папке `webapp/`:
- ✅ `index.html` - готов
- ✅ `styles.css` - готов  
- ✅ `app_standalone.js` - готов (автономная версия с мок-данными)

## 🔄 Быстрое обновление URL в боте

```python
# Найдите эту строку в main.py:
markup.add(types.InlineKeyboardButton("📱 Открыть Mini App", web_app=types.WebAppInfo(url="НОВЫЙ_URL")))

# Замените НОВЫЙ_URL на ваш HTTPS URL
```

## ⚠️ Важно

- URL должен быть HTTPS (HTTP не работает в Telegram)
- Проверьте доступность URL в браузере
- Перезапустите бота после изменения URL

---

**Выберите любой метод и через 5 минут у вас будет рабочий Mini App в боте!** 🎉
