# Changelog

## Версия 2.0 - Улучшенная версия (Текущая)

### ✨ Новые функции
- **Редактирование профиля**: Полная система редактирования всех полей анкеты
- **Детальная статистика**: Показ лайков отправленных/полученных, дизлайков, совпадений
- **Команды администратора**: Статистика бота для админов
- **Улучшенная справка**: Более подробная информация о возможностях

### 🔧 Исправления
- **Исправлена ошибка с callback "skip_photo"**: Правильная обработка пропуска фото
- **Защита от самолайков**: Пользователи не могут лайкать свои анкеты
- **Улучшенная обработка ошибок**: Более стабильная работа бота
- **Валидация данных**: Проверка всех вводимых пользователем данных

### 🎨 Улучшения интерфейса
- **Кнопка статистики в главном меню**: Быстрый доступ к статистике
- **Улучшенные сообщения**: Более понятные и красивые тексты
- **Эмодзи в интерфейсе**: Более привлекательный внешний вид
- **Структурированные меню**: Логичное расположение кнопок

### 🗄️ База данных
- **Новые функции статистики**: Детальная аналитика активности
- **Оптимизированные запросы**: Более быстрая работа с данными
- **Фильтрация пользователей**: Исключение уже оцененных анкет

---

## Версия 1.0 - Базовая версия

### 🎯 Основные функции
- **Регистрация пользователей**: Создание анкеты с именем, возрастом, городом, описанием и фото
- **Просмотр анкет**: Система просмотра анкет других пользователей
- **Лайки и дизлайки**: Оценка анкет с помощью кнопок
- **Взаимные симпатии**: Уведомления о совпадениях
- **База данных SQLite**: Хранение всех данных пользователей

### 📱 Команды
- `/start` - Начало работы с ботом
- `/help` - Справка
- `/profile` - Просмотр своей анкеты

### 🏗️ Архитектура
- Модульная структура кода
- Разделение логики по файлам
- Конфигурационные файлы
- Система состояний пользователей
