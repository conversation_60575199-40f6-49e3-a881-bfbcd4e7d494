#!/usr/bin/env python3
"""
Скрипт для автоматического деплоя Mini App
"""

import os
import shutil
import webbrowser
import time

def create_github_pages_instructions():
    """Создает инструкции для GitHub Pages"""
    instructions = """
🚀 ИНСТРУКЦИИ ПО ДЕПЛОЮ MINI APP

📋 Вариант 1: Git<PERSON>ub Pages (Рекомендуется)
===========================================

1. Создайте новый репозиторий на GitHub:
   - Название: dating-bot-miniapp
   - Публичный репозиторий
   - Инициализируйте с README

2. Загрузите файлы:
   - Скопируйте файл deploy/index.html в корень репозитория
   - Переименуйте его просто в index.html

3. Включите GitHub Pages:
   - Settings → Pages
   - Source: Deploy from a branch
   - Branch: main
   - Folder: / (root)
   - Save

4. Получите URL:
   - Ваш URL будет: https://USERNAME.github.io/dating-bot-miniapp
   - Замените USERNAME на ваш GitHub логин

5. Обновите бота:
   - Откройте main.py
   - Найдите строку с web_app=types.WebAppInfo(url="...")
   - Замените URL на ваш GitHub Pages URL

6. Перезапустите бота:
   - python main.py

📋 Вариант 2: Netlify Drop (Быстрый)
====================================

1. Откройте: https://app.netlify.com/drop
2. Перетащите папку 'deploy' в область загрузки
3. Получите URL (например: https://amazing-app-123456.netlify.app)
4. Обновите URL в main.py
5. Перезапустите бота

📋 Вариант 3: Vercel (Альтернатива)
===================================

1. Откройте: https://vercel.com
2. Войдите через GitHub
3. Import Project → выберите репозиторий
4. Deploy
5. Получите URL

⚠️ ВАЖНО:
- URL должен быть HTTPS
- Проверьте доступность в браузере
- Telegram не принимает localhost URLs

🎯 ТЕСТИРОВАНИЕ:
1. Откройте полученный URL в браузере
2. Проверьте работу Mini App
3. Откройте бота в Telegram
4. Нажмите "📱 Открыть Mini App"
5. Наслаждайтесь!

📁 Готовые файлы находятся в папке 'deploy/'
"""
    
    with open('DEPLOY_INSTRUCTIONS.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Инструкции созданы в файле DEPLOY_INSTRUCTIONS.txt")

def open_deploy_sites():
    """Открывает сайты для деплоя"""
    sites = [
        "https://app.netlify.com/drop",
        "https://github.com/new",
        "https://vercel.com"
    ]
    
    print("🌐 Открываю сайты для деплоя...")
    for site in sites:
        webbrowser.open(site)
        time.sleep(1)

def main():
    print("🚀 Подготовка к деплою Mini App")
    print("=" * 50)
    
    # Проверяем наличие файлов
    if not os.path.exists('deploy/index.html'):
        print("❌ Файл deploy/index.html не найден!")
        return
    
    print("✅ Файлы готовы к деплою:")
    print("   - deploy/index.html (основное приложение)")
    print("   - deploy/netlify.toml (конфигурация)")
    print("   - deploy/README.md (инструкции)")
    
    # Создаем инструкции
    create_github_pages_instructions()
    
    print("\n📋 Выберите способ деплоя:")
    print("1. GitHub Pages (рекомендуется)")
    print("2. Netlify Drop (быстро)")
    print("3. Vercel (альтернатива)")
    print("4. Открыть все сайты")
    print("5. Только инструкции")
    
    choice = input("\nВведите номер (1-5): ").strip()
    
    if choice == "1":
        print("\n🔗 Открываю GitHub для создания репозитория...")
        webbrowser.open("https://github.com/new")
        print("\n📋 Следуйте инструкциям в файле DEPLOY_INSTRUCTIONS.txt")
        
    elif choice == "2":
        print("\n🔗 Открываю Netlify Drop...")
        webbrowser.open("https://app.netlify.com/drop")
        print("\n📋 Перетащите папку 'deploy' в область загрузки")
        
    elif choice == "3":
        print("\n🔗 Открываю Vercel...")
        webbrowser.open("https://vercel.com")
        print("\n📋 Войдите и импортируйте проект")
        
    elif choice == "4":
        open_deploy_sites()
        
    elif choice == "5":
        print("\n📋 Инструкции созданы в DEPLOY_INSTRUCTIONS.txt")
        
    else:
        print("❌ Неверный выбор")
        return
    
    print("\n" + "=" * 50)
    print("🎯 СЛЕДУЮЩИЕ ШАГИ:")
    print("1. Получите HTTPS URL после деплоя")
    print("2. Обновите URL в main.py")
    print("3. Перезапустите бота: python main.py")
    print("4. Протестируйте в Telegram")
    print("=" * 50)

if __name__ == "__main__":
    main()
