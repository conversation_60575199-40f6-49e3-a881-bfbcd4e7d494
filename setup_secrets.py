#!/usr/bin/env python3
"""
Скрипт для быстрой настройки GitHub Secrets
"""

import webbrowser
import time

def main():
    print("🔐 НАСТРОЙКА GITHUB SECRETS ДЛЯ АВТОМАТИЧЕСКОГО ДЕПЛОЯ")
    print("=" * 60)
    
    print("\n📋 У вас есть:")
    print("✅ Репозиторий: https://github.com/KampeLoL/znakomstva_bot")
    print("✅ Netlify Auth Token: ****************************************")
    print("✅ Site ID: fluffy-crumble-05e0fb")
    
    print("\n🎯 ПЛАН ДЕЙСТВИЙ:")
    print("1. Открыть страницу настройки секретов")
    print("2. Добавить NETLIFY_AUTH_TOKEN")
    print("3. Добавить NETLIFY_SITE_ID")
    print("4. Загрузить workflow файл")
    print("5. Протестировать деплой")
    
    choice = input("\n❓ Начать настройку? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        print("\n🌐 Открываю страницу настройки секретов...")
        webbrowser.open("https://github.com/KampeLoL/znakomstva_bot/settings/secrets/actions")
        
        print("\n📋 ШАГ 1: ДОБАВЛЕНИЕ NETLIFY_AUTH_TOKEN")
        print("-" * 50)
        print("1. Нажмите 'New repository secret'")
        print("2. Name: NETLIFY_AUTH_TOKEN")
        print("3. Secret: ****************************************")
        print("4. Нажмите 'Add secret'")
        
        input("\n⏳ Добавьте первый секрет и нажмите Enter...")
        
        print("\n📋 ШАГ 2: ДОБАВЛЕНИЕ NETLIFY_SITE_ID")
        print("-" * 50)
        print("1. Нажмите 'New repository secret' еще раз")
        print("2. Name: NETLIFY_SITE_ID")
        print("3. Secret: fluffy-crumble-05e0fb")
        print("4. Нажмите 'Add secret'")
        
        input("\n⏳ Добавьте второй секрет и нажмите Enter...")
        
        print("\n📋 ШАГ 3: ЗАГРУЗКА WORKFLOW ФАЙЛА")
        print("-" * 50)
        print("1. Откройте ваш репозиторий")
        print("2. Создайте папку .github/workflows/")
        print("3. Загрузите файл deploy-miniapp.yml")
        print("4. Или скопируйте содержимое из созданного файла")
        
        print("\n🌐 Открываю репозиторий...")
        webbrowser.open("https://github.com/KampeLoL/znakomstva_bot")
        
        input("\n⏳ Загрузите workflow файл и нажмите Enter...")
        
        print("\n📋 ШАГ 4: ТЕСТИРОВАНИЕ")
        print("-" * 50)
        print("1. Внесите любое изменение в deploy/index.html")
        print("2. Сделайте commit и push")
        print("3. Проверьте Actions")
        
        print("\n🌐 Открываю страницу Actions...")
        webbrowser.open("https://github.com/KampeLoL/znakomstva_bot/actions")
        
        print("\n🎉 НАСТРОЙКА ЗАВЕРШЕНА!")
        print("=" * 60)
        print("✅ Секреты добавлены:")
        print("   • NETLIFY_AUTH_TOKEN")
        print("   • NETLIFY_SITE_ID")
        print()
        print("✅ Workflow файл загружен:")
        print("   • .github/workflows/deploy-miniapp.yml")
        print()
        print("🔄 Теперь при каждом изменении в deploy/:")
        print("   • Автоматический деплой на Netlify")
        print("   • Обновление Mini App")
        print("   • Уведомления о статусе")
        print()
        print("🧪 ТЕСТИРОВАНИЕ:")
        print("   1. Измените deploy/index.html")
        print("   2. Commit и push")
        print("   3. Проверьте Actions")
        print("   4. Убедитесь, что сайт обновился")
        print()
        print("🔗 Полезные ссылки:")
        print("   📊 Actions: https://github.com/KampeLoL/znakomstva_bot/actions")
        print("   🔐 Secrets: https://github.com/KampeLoL/znakomstva_bot/settings/secrets/actions")
        print("   📱 Mini App: https://fluffy-crumble-05e0fb.netlify.app")
        print("   📖 Инструкции: MINIAPP_DEPLOY.md")
        
    else:
        print("\n📋 Ручная настройка:")
        print("1. Откройте: https://github.com/KampeLoL/znakomstva_bot/settings/secrets/actions")
        print("2. Добавьте секреты согласно MINIAPP_DEPLOY.md")
        print("3. Загрузите workflow файл")
        
    print("\n" + "=" * 60)
    print("🚀 Автоматический деплой готов к работе!")

if __name__ == "__main__":
    main()
