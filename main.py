import telebot
from telebot import types
import sqlite3
import os
from config import BOT_TOKEN, DATABASE_NAME
from database import init_database, create_user, get_user, update_user_field
from user_registration import handle_registration_process, handle_skip_photo_callback, start_registration
from profile_viewing import show_profiles, handle_like_dislike
from matches import show_user_matches, show_user_stats

# Инициализация бота
bot = telebot.TeleBot(BOT_TOKEN)

# Инициализация базы данных при запуске
init_database()

# Словарь для хранения состояний пользователей
user_states = {}

# Состояния пользователей
class UserState:
    MAIN_MENU = "main_menu"
    REGISTRATION = "registration"
    VIEWING_PROFILES = "viewing_profiles"
    EDITING_PROFILE = "editing_profile"

@bot.message_handler(commands=['start'])
def start_command(message):
    """Обработчик команды /start"""
    user_id = message.from_user.id
    print(f"Команда /start от пользователя {user_id}")
    user = get_user(user_id)
    print(f"Пользователь в БД: {user}")

    if user is None:
        # Новый пользователь - начинаем регистрацию
        welcome_text = """
🌟 Добро пожаловать в бот знакомств! 🌟

Здесь вы можете найти интересных людей для общения и знакомств.

Для начала нужно создать вашу анкету. Это займет всего несколько минут!

Нажмите кнопку ниже, чтобы начать регистрацию.
        """
        
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("📝 Создать анкету", callback_data="start_registration"))
        
        bot.send_message(message.chat.id, welcome_text, reply_markup=markup)
    else:
        # Существующий пользователь - показываем главное меню
        show_main_menu(message.chat.id)

@bot.message_handler(commands=['help'])
def help_command(message):
    """Обработчик команды /help"""
    help_text = """
🆘 Помощь по боту знакомств

Доступные команды:
/start - Начать работу с ботом
/help - Показать это сообщение
/profile - Посмотреть свою анкету
/edit - Редактировать анкету
/search - Искать анкеты

Как пользоваться ботом:
1. Создайте свою анкету
2. Просматривайте анкеты других пользователей
3. Ставьте лайки понравившимся анкетам
4. При взаимной симпатии получите уведомление!

По вопросам обращайтесь к администратору.
    """
    bot.send_message(message.chat.id, help_text)

@bot.message_handler(commands=['profile'])
def profile_command(message):
    """Показать профиль пользователя"""
    user_id = message.from_user.id
    user = get_user(user_id)
    
    if user is None:
        bot.send_message(message.chat.id, "❌ У вас нет анкеты. Используйте /start для создания.")
        return
    
    show_user_profile(message.chat.id, user)

def show_main_menu(chat_id):
    """Показать главное меню"""
    menu_text = """
🏠 Главное меню

Выберите действие:
    """
    
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👀 Смотреть анкеты", callback_data="view_profiles"),
        types.InlineKeyboardButton("👤 Моя анкета", callback_data="my_profile")
    )
    markup.add(
        types.InlineKeyboardButton("✏️ Редактировать анкету", callback_data="edit_profile"),
        types.InlineKeyboardButton("❤️ Мои лайки", callback_data="my_likes")
    )
    
    bot.send_message(chat_id, menu_text, reply_markup=markup)

def show_user_profile(chat_id, user):
    """Показать анкету пользователя"""
    profile_text = f"""
👤 Ваша анкета:

📝 Имя: {user[2]}
🎂 Возраст: {user[3]}
🏙️ Город: {user[4]}
📖 О себе: {user[5]}
    """
    
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("✏️ Редактировать", callback_data="edit_profile"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    if user[6]:  # Если есть фото
        try:
            bot.send_photo(chat_id, user[6], caption=profile_text, reply_markup=markup)
        except:
            bot.send_message(chat_id, profile_text, reply_markup=markup)
    else:
        bot.send_message(chat_id, profile_text, reply_markup=markup)

@bot.callback_query_handler(func=lambda call: True)
def callback_handler(call):
    """Обработчик callback запросов"""
    try:
        print(f"Получен callback: {call.data} от пользователя {call.from_user.id}")
        if call.data == "start_registration":
            print("Начинаем регистрацию...")
            start_registration(bot, call.message.chat.id, call.from_user.id, user_states)
        elif call.data == "main_menu":
            show_main_menu(call.message.chat.id)
        elif call.data == "my_profile":
            user = get_user(call.from_user.id)
            if user:
                show_user_profile(call.message.chat.id, user)
        elif call.data == "view_profiles":
            show_profiles(bot, call.message, user_states)
        elif call.data.startswith("like_") or call.data.startswith("dislike_") or call.data.startswith("skip_"):
            handle_like_dislike(bot, call, user_states)
        elif call.data == "skip_photo":
            handle_skip_photo_callback(bot, call, user_states)
        elif call.data == "edit_profile":
            # TODO: Реализовать редактирование профиля
            bot.answer_callback_query(call.id, "Функция в разработке")
        elif call.data == "my_likes":
            show_user_matches(bot, call.message.chat.id, call.from_user.id)
        
        bot.answer_callback_query(call.id)
    except Exception as e:
        print(f"Ошибка в callback_handler: {e}")
        bot.answer_callback_query(call.id, "Произошла ошибка")

@bot.message_handler(content_types=['text', 'photo'])
def message_handler(message):
    """Обработчик текстовых сообщений и фото"""
    user_id = message.from_user.id
    print(f"Получено сообщение от {user_id}: {message.text}")
    
    if user_id in user_states:
        state = user_states[user_id]
        
        if state == UserState.REGISTRATION:
            handle_registration_process(bot, message, user_states)
        else:
            bot.send_message(message.chat.id, "Используйте кнопки меню для навигации.")
    else:
        bot.send_message(message.chat.id, "Используйте /start для начала работы с ботом.")

if __name__ == "__main__":
    print("🤖 Бот запущен!")
    print(f"Токен бота: {BOT_TOKEN[:10]}...")
    print("Инициализация базы данных...")
    try:
        print("Запуск polling...")
        bot.polling(none_stop=True)
    except Exception as e:
        print(f"Ошибка при запуске бота: {e}")
        import traceback
        traceback.print_exc()
