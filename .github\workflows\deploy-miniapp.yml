name: 🚀 Deploy Mini App to Netlify

on:
  push:
    branches: [ main, master ]
    paths: 
      - 'deploy/**'
      - '.github/workflows/deploy-miniapp.yml'
  pull_request:
    branches: [ main, master ]
    paths: 
      - 'deploy/**'
  workflow_dispatch:

jobs:
  deploy:
    name: 📱 Build and Deploy Mini App
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: 📦 Install Netlify CLI
      run: |
        npm install -g netlify-cli
        
    - name: 🏗️ Prepare Mini App
      run: |
        echo "✅ Preparing Mini App for deployment..."
        ls -la deploy/
        
    - name: 🧪 Test HTML validity
      run: |
        echo "🧪 Testing Mini App HTML..."
        if [ -f "deploy/index.html" ]; then
          echo "✅ index.html found"
          # Проверяем базовую структуру HTML
          if grep -q "<!DOCTYPE html>" deploy/index.html; then
            echo "✅ Valid HTML structure"
          else
            echo "❌ Invalid HTML structure"
            exit 1
          fi
          # Проверяем наличие Telegram Web App API
          if grep -q "telegram-web-app.js" deploy/index.html; then
            echo "✅ Telegram Web App API found"
          else
            echo "⚠️ Warning: Telegram Web App API not found"
          fi
        else
          echo "❌ deploy/index.html not found"
          exit 1
        fi
        
    - name: 🚀 Deploy to Netlify
      uses: nwtgck/actions-netlify@v3.0
      with:
        publish-dir: './deploy'
        production-branch: main
        github-token: ${{ secrets.GITHUB_TOKEN }}
        deploy-message: "🚀 Auto-deploy Mini App from commit ${{ github.sha }}"
        enable-pull-request-comment: true
        enable-commit-comment: true
        overwrites-pull-request-comment: true
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        
    - name: 📊 Deploy Status
      run: |
        echo "🎉 Mini App deployment completed!"
        echo "📱 Mini App URL: https://${{ secrets.NETLIFY_SITE_ID }}.netlify.app"
        echo "🤖 Make sure to update this URL in your Telegram bot (main.py)"
        
    - name: 🔔 Success Notification
      if: success()
      run: |
        echo "✅ SUCCESS: Mini App deployed successfully!"
        echo "🔗 Live URL: https://${{ secrets.NETLIFY_SITE_ID }}.netlify.app"
        echo "📝 Next steps:"
        echo "   1. Test the Mini App in browser"
        echo "   2. Update URL in main.py if needed"
        echo "   3. Test in Telegram bot"
        
    - name: 🚨 Failure Notification
      if: failure()
      run: |
        echo "❌ FAILED: Mini App deployment failed!"
        echo "📋 Check the logs above for details"
        echo "🔧 Common issues:"
        echo "   - Check NETLIFY_AUTH_TOKEN secret"
        echo "   - Check NETLIFY_SITE_ID secret"
        echo "   - Verify deploy/index.html exists"
