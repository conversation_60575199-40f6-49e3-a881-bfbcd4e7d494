# 📁 Загрузка файлов в GitHub

## 🚀 Быстрая загрузка workflow файла

### Способ 1: Через веб-интерфейс GitHub

1. **Откройте ваш репозиторий**: https://github.com/KampeLoL/znakomstva_bot

2. **Создайте папку .github/workflows/**:
   - Нажмите "Create new file"
   - В поле имени файла введите: `.github/workflows/deploy-miniapp.yml`
   - GitHub автоматически создаст папки

3. **Скопируйте содержимое файла**:
   - Откройте файл `.github/workflows/deploy-miniapp.yml` в вашей локальной папке
   - Скопируйте все содержимое
   - Вставьте в GitHub

4. **Сохраните**:
   - Commit message: "Add GitHub Actions workflow for Mini App auto-deploy"
   - Нажмите "Commit new file"

### Способ 2: Через Git команды

```bash
# Если у вас настроен Git
git add .github/workflows/deploy-miniapp.yml
git commit -m "Add GitHub Actions workflow for Mini App auto-deploy"
git push origin main
```

## ✅ После загрузки

1. **Проверьте Actions**: https://github.com/KampeLoL/znakomstva_bot/actions
2. **Workflow должен появиться**: "🚀 Deploy Mini App to Netlify"
3. **Сделайте тестовое изменение** в `deploy/index.html`
4. **Commit и push** - деплой запустится автоматически

## 🎯 Тестирование

1. Измените любую строку в `deploy/index.html`
2. Commit: "Test auto-deploy"
3. Push в main ветку
4. Проверьте Actions - должен запуститься workflow
5. После успешного деплоя проверьте: https://fluffy-crumble-05e0fb.netlify.app

---

**Автоматический деплой будет работать сразу после загрузки файла!** 🚀
