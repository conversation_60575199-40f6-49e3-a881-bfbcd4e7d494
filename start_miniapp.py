#!/usr/bin/env python3
"""
Скрипт для запуска полной системы: бот + Mini App сервер
"""

import subprocess
import sys
import time
import os
import signal
import threading

def start_bot():
    """Запуск основного бота"""
    print("🤖 Запуск основного бота...")
    return subprocess.Popen([sys.executable, "main.py"], cwd=os.getcwd())

def start_miniapp_server():
    """Запуск Mini App сервера"""
    print("📱 Запуск Mini App сервера...")
    return subprocess.Popen([sys.executable, "local_miniapp_server.py"], cwd=os.getcwd())

def signal_handler(sig, frame):
    """Обработчик сигнала для корректного завершения"""
    print("\n🛑 Получен сигнал завершения...")
    global bot_process, server_process
    
    if bot_process:
        print("🤖 Завершение бота...")
        bot_process.terminate()
        bot_process.wait()
    
    if server_process:
        print("📱 Завершение Mini App сервера...")
        server_process.terminate()
        server_process.wait()
    
    print("✅ Все процессы завершены")
    sys.exit(0)

def main():
    global bot_process, server_process
    
    print("🚀 Запуск полной системы бота знакомств с Mini App")
    print("=" * 60)
    
    # Проверяем наличие необходимых файлов
    required_files = [
        "main.py",
        "local_miniapp_server.py", 
        "database.py",
        "config.py",
        "webapp/index.html",
        "webapp/styles.css",
        "webapp/app.js"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Отсутствуют необходимые файлы:")
        for file in missing_files:
            print(f"   - {file}")
        return
    
    # Регистрируем обработчик сигналов
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Запускаем Mini App сервер
        server_process = start_miniapp_server()
        time.sleep(3)  # Даем серверу время запуститься
        
        # Запускаем основной бот
        bot_process = start_bot()
        time.sleep(2)  # Даем боту время запуститься
        
        print("\n" + "=" * 60)
        print("✅ Система успешно запущена!")
        print("📱 Mini App сервер: https://localhost:5000")
        print("🤖 Telegram бот: работает")
        print("=" * 60)
        print("\n📋 Инструкции по использованию:")
        print("1. Откройте бота в Telegram")
        print("2. Нажмите кнопку '📱 Открыть Mini App'")
        print("3. Если браузер показывает предупреждение о безопасности - нажмите 'Продолжить'")
        print("4. Наслаждайтесь полнофункциональным Mini App!")
        print("\n⚠️  Для остановки нажмите Ctrl+C")
        print("=" * 60)
        
        # Ожидаем завершения процессов
        while True:
            # Проверяем, что процессы еще работают
            if bot_process.poll() is not None:
                print("❌ Бот завершился неожиданно")
                break
            
            if server_process.poll() is not None:
                print("❌ Mini App сервер завершился неожиданно")
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        signal_handler(signal.SIGTERM, None)

if __name__ == "__main__":
    bot_process = None
    server_process = None
    main()
