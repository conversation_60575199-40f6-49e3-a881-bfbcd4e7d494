# 🚀 Инструкции по развертыванию Mini App

## 📱 Быстрый деплой на Netlify

### 1. **Подготовка файлов:**
Все файлы Mini App находятся в папке `webapp/`:
- `index.html` - основная страница
- `styles.css` - стили
- `app_standalone.js` - JavaScript логика (автономная версия)

### 2. **Деплой на Netlify:**

#### Вариант A: Через веб-интерфейс
1. Зайдите на https://netlify.com
2. Зарегистрируйтесь/войдите
3. Перетащите папку `webapp` в область "Deploy"
4. Получите HTTPS URL (например: https://amazing-app-123.netlify.app)

#### Вариант B: Через GitHub
1. Создайте репозиторий на GitHub
2. Загрузите файлы проекта
3. Подключите репозиторий к Netlify
4. Настройте автодеплой

### 3. **Обновление URL в боте:**
```python
# В файле main.py замените URL:
markup.add(types.InlineKeyboardButton(
    "📱 Открыть Mini App", 
    web_app=types.WebAppInfo(url="https://ваш-домен.netlify.app")
))
```

### 4. **Альтернативные платформы:**

#### **Vercel:**
1. https://vercel.com
2. Импорт из GitHub
3. Автоматический деплой

#### **GitHub Pages:**
1. Создайте репозиторий `username.github.io`
2. Загрузите файлы из папки `webapp`
3. URL: https://username.github.io

#### **Railway:**
1. https://railway.app
2. Деплой из GitHub
3. Автоматический HTTPS

## 🔧 Локальное тестирование

### Простой HTTP сервер:
```bash
cd webapp
python -m http.server 8000
```
Доступ: http://localhost:8000

### HTTPS сервер (требует сертификат):
```bash
python https_server.py
```

## 📋 Чек-лист деплоя

- [ ] Файлы Mini App готовы в папке `webapp/`
- [ ] Выбрана платформа для хостинга
- [ ] Получен HTTPS URL
- [ ] URL обновлен в коде бота (`main.py`)
- [ ] Бот перезапущен
- [ ] Mini App протестирован в Telegram

## 🎯 Тестирование

1. Запустите бота: `python main.py`
2. Откройте бота в Telegram
3. Нажмите "📱 Открыть Mini App"
4. Проверьте все функции:
   - Свайп анкет
   - Навигация между разделами
   - Модальные окна
   - Анимации

## 🔐 Безопасность

- Все данные в автономной версии - мок-данные
- Для продакшена нужно подключить реальный API
- Telegram автоматически проверяет HTTPS сертификаты

## 📞 Поддержка

При проблемах с деплоем:
1. Проверьте логи платформы
2. Убедитесь, что все файлы загружены
3. Проверьте HTTPS доступность
4. Протестируйте в браузере перед Telegram

---

**Mini App готов к деплою!** 🎉
