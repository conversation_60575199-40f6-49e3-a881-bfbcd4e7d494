#!/usr/bin/env python3
"""
Локальный HTTPS сервер для Mini App с полной интеграцией базы данных
"""

from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS
import ssl
import os
import json
import hashlib
import hmac
from urllib.parse import unquote
from database import get_user, get_users_for_viewing, add_reaction, get_user_matches, get_user_activity_stats, update_user_field
from config import BOT_TOKEN

app = Flask(__name__, static_folder='webapp', template_folder='webapp')
CORS(app)

# Функция для проверки данных Telegram Web App
def verify_telegram_data(init_data, bot_token):
    """Проверка подлинности данных от Telegram Web App"""
    try:
        if not init_data:
            return False
            
        # Парсим данные
        data_dict = {}
        for item in init_data.split('&'):
            if '=' in item:
                key, value = item.split('=', 1)
                data_dict[key] = unquote(value)
        
        # Извлекаем hash
        received_hash = data_dict.pop('hash', '')
        if not received_hash:
            return False
        
        # Создаем строку для проверки
        data_check_string = '\n'.join([f"{k}={v}" for k, v in sorted(data_dict.items())])
        
        # Создаем секретный ключ
        secret_key = hmac.new(b"WebAppData", bot_token.encode(), hashlib.sha256).digest()
        
        # Вычисляем hash
        calculated_hash = hmac.new(secret_key, data_check_string.encode(), hashlib.sha256).hexdigest()
        
        return calculated_hash == received_hash
    except Exception as e:
        print(f"Ошибка проверки данных Telegram: {e}")
        return False

def get_user_from_init_data(init_data):
    """Извлечение данных пользователя из init_data"""
    try:
        if not init_data:
            # Для локального тестирования возвращаем тестового пользователя
            return {'id': 826867374, 'first_name': 'Тестовый пользователь'}
            
        data_dict = {}
        for item in init_data.split('&'):
            if '=' in item:
                key, value = item.split('=', 1)
                data_dict[key] = unquote(value)
        
        if 'user' in data_dict:
            user_data = json.loads(data_dict['user'])
            return user_data
        return None
    except Exception as e:
        print(f"Ошибка извлечения пользователя: {e}")
        # Для локального тестирования возвращаем тестового пользователя
        return {'id': 826867374, 'first_name': 'Тестовый пользователь'}

@app.route('/')
def index():
    """Главная страница Mini App"""
    return send_from_directory('webapp', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """Статические файлы"""
    return send_from_directory('webapp', filename)

@app.route('/api/user', methods=['GET'])
def get_user_profile():
    """Получение профиля пользователя"""
    try:
        # Получаем данные авторизации
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        
        # Извлекаем данные пользователя
        telegram_user = get_user_from_init_data(init_data)
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        user = get_user(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'id': user[0],
            'name': user[2],
            'age': user[3],
            'city': user[4],
            'description': user[5],
            'photo': user[6]
        })
        
    except Exception as e:
        print(f"Ошибка получения профиля: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/profiles', methods=['GET'])
def get_profiles():
    """Получение анкет для просмотра"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        profiles = get_users_for_viewing(user_id, limit=10)
        
        # Преобразуем в JSON формат
        profiles_data = []
        for profile in profiles:
            profiles_data.append({
                'id': profile[0],
                'name': profile[2],
                'age': profile[3],
                'city': profile[4],
                'description': profile[5],
                'photo': profile[6]
            })
        
        return jsonify({'profiles': profiles_data})
        
    except Exception as e:
        print(f"Ошибка получения анкет: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/reaction', methods=['POST'])
def send_reaction():
    """Отправка реакции (лайк/дизлайк)"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        data = request.get_json()
        user_id = telegram_user['id']
        target_user_id = data.get('target_user_id')
        reaction_type = data.get('reaction_type')  # 'like' или 'dislike'
        
        if not target_user_id or not reaction_type:
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Добавляем реакцию
        is_match = add_reaction(user_id, target_user_id, reaction_type)
        
        response = {
            'success': True,
            'is_match': is_match
        }
        
        if is_match:
            # Получаем данные о совпадении
            target_user = get_user(target_user_id)
            if target_user:
                response['match_user'] = {
                    'id': target_user[0],
                    'name': target_user[2],
                    'age': target_user[3],
                    'city': target_user[4]
                }
        
        return jsonify(response)
        
    except Exception as e:
        print(f"Ошибка отправки реакции: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/matches', methods=['GET'])
def get_matches():
    """Получение совпадений пользователя"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        matches = get_user_matches(user_id)
        
        matches_data = []
        for match in matches:
            matches_data.append({
                'id': match[0],
                'name': match[2],
                'age': match[3],
                'city': match[4],
                'photo': match[6]
            })
        
        return jsonify({'matches': matches_data})
        
    except Exception as e:
        print(f"Ошибка получения совпадений: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Получение статистики пользователя"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        stats = get_user_activity_stats(user_id)
        
        return jsonify(stats)
        
    except Exception as e:
        print(f"Ошибка получения статистики: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/profile', methods=['PUT'])
def update_profile():
    """Обновление профиля пользователя"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        data = request.get_json()
        user_id = telegram_user['id']
        
        # Обновляем поля профиля
        if 'name' in data:
            update_user_field(user_id, 'name', data['name'])
        if 'age' in data:
            update_user_field(user_id, 'age', data['age'])
        if 'city' in data:
            update_user_field(user_id, 'city', data['city'])
        if 'description' in data:
            update_user_field(user_id, 'description', data['description'])
        
        return jsonify({'success': True})
        
    except Exception as e:
        print(f"Ошибка обновления профиля: {e}")
        return jsonify({'error': 'Internal server error'}), 500

def create_simple_cert():
    """Создание простого самоподписанного сертификата"""
    try:
        import subprocess
        import sys
        
        # Проверяем, есть ли уже сертификат
        if os.path.exists('cert.pem') and os.path.exists('key.pem'):
            print("✅ Сертификат уже существует")
            return True
        
        # Создаем сертификат с помощью OpenSSL (если доступен)
        try:
            subprocess.run([
                'openssl', 'req', '-x509', '-newkey', 'rsa:4096', '-keyout', 'key.pem', 
                '-out', 'cert.pem', '-days', '365', '-nodes', '-subj', 
                '/C=RU/ST=Moscow/L=Moscow/O=DatingBot/CN=localhost'
            ], check=True, capture_output=True)
            print("✅ Сертификат создан с помощью OpenSSL")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ OpenSSL не найден, используем Python cryptography")
        
        # Fallback на Python cryptography
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import datetime
        
        # Генерируем приватный ключ
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # Создаем сертификат
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "RU"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Moscow"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Moscow"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Dating Bot"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.now(datetime.timezone.utc)
        ).not_valid_after(
            datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("127.0.0.1"),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # Сохраняем сертификат и ключ
        with open("cert.pem", "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        with open("key.pem", "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        print("✅ Самоподписанный сертификат создан!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка создания сертификата: {e}")
        return False

if __name__ == '__main__':
    print("🔐 Запуск локального HTTPS сервера для Mini App...")
    
    # Создаем сертификат
    if not create_simple_cert():
        print("❌ Не удалось создать сертификат. Запускаем HTTP сервер...")
        print("🌐 HTTP сервер запущен на http://localhost:5000")
        print("⚠️ Для работы в Telegram нужен HTTPS!")
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        # Создаем SSL контекст
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('cert.pem', 'key.pem')
        
        print("🌐 HTTPS сервер запущен!")
        print("📱 Mini App доступен по адресу: https://localhost:5000")
        print("⚠️ Браузер покажет предупреждение о безопасности - нажмите 'Продолжить'")
        print("🔗 Используйте этот URL в боте: https://localhost:5000")
        
        app.run(host='0.0.0.0', port=5000, debug=False, ssl_context=context)
