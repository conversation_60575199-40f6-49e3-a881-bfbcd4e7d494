#!/usr/bin/env python3
"""
Веб-сервер для Mini App бота знакомств
"""

from flask import Flask, render_template, send_from_directory, jsonify, request
from flask_cors import CORS
import os
import json
import hashlib
import hmac
from urllib.parse import unquote
from database import get_user, get_users_for_viewing, add_reaction, get_user_matches, get_user_activity_stats
from config import BOT_TOKEN

app = Flask(__name__, static_folder='webapp', template_folder='webapp')
CORS(app)

# Функция для проверки данных Telegram Web App
def verify_telegram_data(init_data, bot_token):
    """Проверка подлинности данных от Telegram Web App"""
    try:
        # Парсим данные
        data_dict = {}
        for item in init_data.split('&'):
            key, value = item.split('=', 1)
            data_dict[key] = unquote(value)
        
        # Извлекаем hash
        received_hash = data_dict.pop('hash', '')
        
        # Создаем строку для проверки
        data_check_string = '\n'.join([f"{k}={v}" for k, v in sorted(data_dict.items())])
        
        # Создаем секретный ключ
        secret_key = hmac.new(b"WebAppData", bot_token.encode(), hashlib.sha256).digest()
        
        # Вычисляем hash
        calculated_hash = hmac.new(secret_key, data_check_string.encode(), hashlib.sha256).hexdigest()
        
        return calculated_hash == received_hash
    except Exception as e:
        print(f"Ошибка проверки данных Telegram: {e}")
        return False

def get_user_from_init_data(init_data):
    """Извлечение данных пользователя из init_data"""
    try:
        data_dict = {}
        for item in init_data.split('&'):
            key, value = item.split('=', 1)
            data_dict[key] = unquote(value)
        
        if 'user' in data_dict:
            user_data = json.loads(data_dict['user'])
            return user_data
        return None
    except Exception as e:
        print(f"Ошибка извлечения пользователя: {e}")
        return None

@app.route('/')
def index():
    """Главная страница Mini App"""
    return send_from_directory('webapp', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """Статические файлы"""
    return send_from_directory('webapp', filename)

@app.route('/api/user', methods=['GET'])
def get_user_profile():
    """Получение профиля пользователя"""
    try:
        # Получаем данные авторизации
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        
        # В продакшене нужно проверять подпись
        # if not verify_telegram_data(init_data, BOT_TOKEN):
        #     return jsonify({'error': 'Unauthorized'}), 401
        
        # Извлекаем данные пользователя
        telegram_user = get_user_from_init_data(init_data)
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        user = get_user(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'id': user[0],
            'name': user[2],
            'age': user[3],
            'city': user[4],
            'description': user[5],
            'photo': user[6]
        })
        
    except Exception as e:
        print(f"Ошибка получения профиля: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/profiles', methods=['GET'])
def get_profiles():
    """Получение анкет для просмотра"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        profiles = get_users_for_viewing(user_id, limit=10)
        
        # Преобразуем в JSON формат
        profiles_data = []
        for profile in profiles:
            profiles_data.append({
                'id': profile[0],
                'name': profile[2],
                'age': profile[3],
                'city': profile[4],
                'description': profile[5],
                'photo': profile[6]
            })
        
        return jsonify({'profiles': profiles_data})
        
    except Exception as e:
        print(f"Ошибка получения анкет: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/reaction', methods=['POST'])
def send_reaction():
    """Отправка реакции (лайк/дизлайк)"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        data = request.get_json()
        user_id = telegram_user['id']
        target_user_id = data.get('target_user_id')
        reaction_type = data.get('reaction_type')  # 'like' или 'dislike'
        
        if not target_user_id or not reaction_type:
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Добавляем реакцию
        is_match = add_reaction(user_id, target_user_id, reaction_type)
        
        response = {
            'success': True,
            'is_match': is_match
        }
        
        if is_match:
            # Получаем данные о совпадении
            target_user = get_user(target_user_id)
            if target_user:
                response['match_user'] = {
                    'id': target_user[0],
                    'name': target_user[2],
                    'age': target_user[3],
                    'city': target_user[4]
                }
        
        return jsonify(response)
        
    except Exception as e:
        print(f"Ошибка отправки реакции: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/matches', methods=['GET'])
def get_matches():
    """Получение совпадений пользователя"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        matches = get_user_matches(user_id)
        
        matches_data = []
        for match in matches:
            matches_data.append({
                'id': match[0],
                'name': match[2],
                'age': match[3],
                'city': match[4],
                'photo': match[6]
            })
        
        return jsonify({'matches': matches_data})
        
    except Exception as e:
        print(f"Ошибка получения совпадений: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Получение статистики пользователя"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        user_id = telegram_user['id']
        stats = get_user_activity_stats(user_id)
        
        return jsonify(stats)
        
    except Exception as e:
        print(f"Ошибка получения статистики: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/profile', methods=['PUT'])
def update_profile():
    """Обновление профиля пользователя"""
    try:
        init_data = request.headers.get('X-Telegram-Init-Data', '')
        telegram_user = get_user_from_init_data(init_data)
        
        if not telegram_user:
            return jsonify({'error': 'No user data'}), 400
        
        data = request.get_json()
        user_id = telegram_user['id']
        
        # Обновляем поля профиля
        from database import update_user_field
        
        if 'name' in data:
            update_user_field(user_id, 'name', data['name'])
        if 'age' in data:
            update_user_field(user_id, 'age', data['age'])
        if 'city' in data:
            update_user_field(user_id, 'city', data['city'])
        if 'description' in data:
            update_user_field(user_id, 'description', data['description'])
        
        return jsonify({'success': True})
        
    except Exception as e:
        print(f"Ошибка обновления профиля: {e}")
        return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🌐 Запуск веб-сервера для Mini App...")
    print("📱 Mini App будет доступен по адресу: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
