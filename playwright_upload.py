#!/usr/bin/env python3
"""
Надежная автоматическая загрузка workflow файла в GitHub через Playwright
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def github_auto_upload():
    """Автоматически загружает workflow файл в GitHub"""
    
    # Читаем содержимое workflow файла
    workflow_path = '.github/workflows/deploy-miniapp.yml'
    if not os.path.exists(workflow_path):
        print("❌ Файл workflow не найден!")
        return False
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_content = f.read()
    
    print("🎭 Запускаю Playwright...")
    
    async with async_playwright() as p:
        # Запускаем браузер с настройками
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=1000,  # Замедляем для надежности
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = await context.new_page()
        
        try:
            print("🔐 Шаг 1: Вход в GitHub...")
            await page.goto("https://github.com/login", wait_until='networkidle')
            
            # Вход в систему
            await page.fill('#login_field', '<EMAIL>')
            print("✅ Email введен")
            
            await page.fill('#password', 'Mum007Mum!')
            print("✅ Пароль введен")
            
            await page.click('input[type="submit"][value="Sign in"]')
            print("🔑 Выполняю вход...")
            
            # Ждем завершения входа или 2FA
            try:
                await page.wait_for_url('https://github.com/', timeout=10000)
                print("✅ Вход выполнен успешно!")
            except:
                # Проверяем 2FA
                if await page.locator('input[name="otp"]').count() > 0:
                    print("🔐 Требуется 2FA - введите код в браузере")
                    await page.wait_for_url('https://github.com/', timeout=120000)
                    print("✅ 2FA подтвержден!")
            
            print("🌐 Шаг 2: Переход к репозиторию...")
            await page.goto("https://github.com/KampeLoL/znakomstva_bot", wait_until='networkidle')
            
            print("📁 Шаг 3: Создание нового файла...")
            
            # Способ 1: Прямой переход к созданию файла
            await page.goto("https://github.com/KampeLoL/znakomstva_bot/new/main", wait_until='networkidle')
            
            # Проверяем, что мы на странице создания файла
            if 'new' not in page.url:
                print("🔄 Пробую альтернативный способ...")
                
                # Способ 2: Через кнопки интерфейса
                await page.goto("https://github.com/KampeLoL/znakomstva_bot")
                await page.wait_for_load_state('networkidle')
                
                # Ищем кнопку Add file
                add_file_button = page.locator('text=Add file').first
                if await add_file_button.count() > 0:
                    await add_file_button.click()
                    await page.locator('text=Create new file').click()
                else:
                    # Способ 3: Через точку меню
                    await page.locator('[data-testid="add-file-button"]').click()
                    await page.locator('text=Create new file').click()
            
            print("📝 Шаг 4: Заполнение формы...")
            
            # Ждем появления поля имени файла
            await page.wait_for_selector('input[name="filename"]', timeout=15000)
            
            # Заполняем имя файла
            await page.fill('input[name="filename"]', '.github/workflows/deploy-miniapp.yml')
            print("✅ Имя файла: .github/workflows/deploy-miniapp.yml")
            
            # Ждем появления редактора
            await page.wait_for_timeout(2000)
            
            # Заполняем содержимое - пробуем разные способы
            content_filled = False
            
            # Способ 1: Обычное textarea
            if await page.locator('textarea[name="value"]').count() > 0:
                await page.fill('textarea[name="value"]', workflow_content)
                content_filled = True
                print("✅ Содержимое заполнено (textarea)")
            
            # Способ 2: CodeMirror
            elif await page.locator('.CodeMirror').count() > 0:
                await page.evaluate(f"""
                    const editor = document.querySelector('.CodeMirror').CodeMirror;
                    editor.setValue(`{workflow_content.replace('`', '\\`').replace('$', '\\$')}`);
                """)
                content_filled = True
                print("✅ Содержимое заполнено (CodeMirror)")
            
            # Способ 3: Monaco Editor
            elif await page.locator('.monaco-editor').count() > 0:
                await page.evaluate(f"""
                    const models = monaco.editor.getModels();
                    if (models.length > 0) {{
                        models[0].setValue(`{workflow_content.replace('`', '\\`').replace('$', '\\$')}`);
                    }}
                """)
                content_filled = True
                print("✅ Содержимое заполнено (Monaco)")
            
            if not content_filled:
                print("⚠️ Пробую универсальный способ...")
                # Универсальный способ через клавиатуру
                await page.keyboard.press('Tab')  # Переход к редактору
                await page.keyboard.press('Control+a')  # Выделить все
                await page.keyboard.type(workflow_content)  # Ввести текст
                print("✅ Содержимое введено через клавиатуру")
            
            print("💬 Шаг 5: Commit message...")
            
            # Заполняем commit message
            commit_title_selector = 'input[name="commit_title"], #commit-summary-input'
            if await page.locator(commit_title_selector).count() > 0:
                await page.fill(commit_title_selector, "Add GitHub Actions workflow for Mini App auto-deploy")
                print("✅ Commit message заполнен")
            
            print("💾 Шаг 6: Сохранение файла...")
            
            # Ищем и нажимаем кнопку Commit
            commit_button_selectors = [
                'button:has-text("Commit new file")',
                'input[value*="Commit new file"]',
                'button[type="submit"]:has-text("Commit")'
            ]
            
            commit_clicked = False
            for selector in commit_button_selectors:
                if await page.locator(selector).count() > 0:
                    await page.locator(selector).click()
                    commit_clicked = True
                    print(f"✅ Нажата кнопка: {selector}")
                    break
            
            if not commit_clicked:
                print("🔍 Ищу кнопку commit по тексту...")
                await page.locator('text=Commit new file').click()
                print("✅ Commit выполнен")
            
            # Ждем перенаправления
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            print("🎉 Шаг 7: Проверка результата...")
            
            # Проверяем успешность
            current_url = page.url
            if '.github/workflows/deploy-miniapp.yml' in current_url or 'znakomstva_bot' in current_url:
                print("✅ Файл успешно создан!")
                
                # Переходим к Actions
                print("📊 Открываю GitHub Actions...")
                await page.goto("https://github.com/KampeLoL/znakomstva_bot/actions")
                await page.wait_for_load_state('networkidle')
                
                print("🎉 УСПЕХ! Автоматический деплой настроен!")
                return True
            else:
                print("⚠️ Возможна ошибка, проверьте результат")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            print(f"🔍 Текущая страница: {page.url}")
            
            # Делаем скриншот для диагностики
            await page.screenshot(path='error_screenshot.png')
            print("📸 Скриншот сохранен: error_screenshot.png")
            
            return False
        
        finally:
            print("\n🔍 Браузер остается открытым для проверки")
            print("⏳ Нажмите Enter для закрытия...")
            input()
            await browser.close()

async def main():
    print("🎭 PLAYWRIGHT АВТОМАТИЧЕСКАЯ ЗАГРУЗКА")
    print("=" * 50)
    
    print("📋 Что будет выполнено:")
    print("1. 🔐 Автоматический вход в GitHub")
    print("2. 🌐 Переход к репозиторию")
    print("3. 📁 Создание файла .github/workflows/deploy-miniapp.yml")
    print("4. 📝 Заполнение содержимого")
    print("5. 💾 Сохранение файла")
    print("6. 📊 Переход к GitHub Actions")
    
    print("\n🔑 Учетные данные:")
    print("📧 Email: <EMAIL>")
    print("🔒 Пароль: [скрыт]")
    print("🔐 2FA: будет запрошен при необходимости")
    
    choice = input("\n❓ Запустить автоматическую загрузку? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        print("\n🚀 Запускаю автоматизацию...")
        success = await github_auto_upload()
        
        if success:
            print("\n🎉 ПОЗДРАВЛЯЮ! ВСЁ ГОТОВО!")
            print("=" * 50)
            print("✅ Workflow файл загружен")
            print("✅ GitHub Actions активирован")
            print("✅ Автоматический деплой настроен")
            
            print("\n🧪 ТЕСТИРОВАНИЕ:")
            print("1. Измените deploy/index.html")
            print("2. Commit и push в репозиторий")
            print("3. Проверьте Actions - деплой запустится автоматически!")
            
            print("\n🔗 Ссылки:")
            print("📊 Actions: https://github.com/KampeLoL/znakomstva_bot/actions")
            print("📱 Mini App: https://fluffy-crumble-05e0fb.netlify.app")
            
        else:
            print("\n❌ Возникла проблема")
            print("📋 Попробуйте:")
            print("1. Проверить скриншот: error_screenshot.png")
            print("2. Загрузить файл вручную")
            print("3. Запустить скрипт еще раз")
    else:
        print("\n📋 Для ручной загрузки:")
        print("1. Откройте: https://github.com/KampeLoL/znakomstva_bot")
        print("2. Create new file")
        print("3. Имя: .github/workflows/deploy-miniapp.yml")
        print("4. Скопируйте содержимое из локального файла")

if __name__ == "__main__":
    asyncio.run(main())
