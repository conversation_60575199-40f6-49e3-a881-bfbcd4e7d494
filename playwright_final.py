#!/usr/bin/env python3
"""
Финальная версия Playwright без evaluate - только DOM манипуляции
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def final_github_upload():
    """Финальная версия без JavaScript evaluate"""
    
    workflow_path = '.github/workflows/deploy-miniapp.yml'
    if not os.path.exists(workflow_path):
        print("❌ Файл workflow не найден!")
        return False
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_content = f.read()
    
    print("🎭 Финальная версия Playwright...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=1500,
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = await context.new_page()
        
        try:
            print("🔐 Вход в GitHub...")
            await page.goto("https://github.com/login")
            
            await page.fill('#login_field', '<EMAIL>')
            await page.fill('#password', 'Mum007Mum!')
            await page.click('input[type="submit"]')
            
            print("⏳ Ожидание входа...")
            await page.wait_for_timeout(5000)
            
            # Проверяем 2FA
            current_url = page.url
            if 'two-factor' in current_url:
                print("🔐 Требуется 2FA - введите код в браузере")
                print("⏳ Ожидание подтверждения...")
                
                # Ждем пока URL изменится (выход из 2FA)
                while 'two-factor' in page.url:
                    await page.wait_for_timeout(2000)
                    print("⏳ Все еще жду 2FA...")
            
            print("✅ Вход выполнен!")
            
            print("🌐 Переход к созданию файла...")
            # Прямой переход к созданию файла
            await page.goto("https://github.com/KampeLoL/znakomstva_bot/new/main")
            await page.wait_for_timeout(3000)
            
            # Проверяем, загрузилась ли страница создания файла
            if await page.locator('input[name="filename"]').count() == 0:
                print("🔄 Альтернативный способ...")
                
                # Переходим к репозиторию
                await page.goto("https://github.com/KampeLoL/znakomstva_bot")
                await page.wait_for_load_state('networkidle')
                
                # Ищем и кликаем Add file
                add_file_locator = page.locator('text=Add file').first
                if await add_file_locator.count() > 0:
                    await add_file_locator.click()
                    await page.wait_for_timeout(1000)
                    
                    # Кликаем Create new file
                    create_file_locator = page.locator('text=Create new file')
                    if await create_file_locator.count() > 0:
                        await create_file_locator.click()
                    else:
                        print("❌ Не найдена опция Create new file")
                        return False
                else:
                    print("❌ Не найдена кнопка Add file")
                    return False
            
            print("📝 Заполнение формы...")
            
            # Ждем поле имени файла
            await page.wait_for_selector('input[name="filename"]', timeout=10000)
            
            # Заполняем имя файла
            await page.fill('input[name="filename"]', '.github/workflows/deploy-miniapp.yml')
            print("✅ Имя файла заполнено")
            
            # Ждем появления редактора
            await page.wait_for_timeout(2000)
            
            print("📄 Заполнение содержимого файла...")
            
            # Пробуем найти textarea
            textarea_locator = page.locator('textarea[name="value"]')
            if await textarea_locator.count() > 0:
                await textarea_locator.fill(workflow_content)
                print("✅ Содержимое заполнено через textarea")
            else:
                # Пробуем через клавиатуру
                print("🔄 Использую клавиатуру для ввода...")
                
                # Кликаем в область редактора
                editor_area = page.locator('.CodeMirror, .monaco-editor, [data-testid="file-editor"]').first
                if await editor_area.count() > 0:
                    await editor_area.click()
                else:
                    # Просто нажимаем Tab для перехода к редактору
                    await page.keyboard.press('Tab')
                
                # Очищаем и вводим содержимое
                await page.keyboard.press('Control+a')
                await page.keyboard.type(workflow_content)
                print("✅ Содержимое введено через клавиатуру")
            
            print("💬 Заполнение commit message...")
            
            # Заполняем commit message
            commit_title_locator = page.locator('input[name="commit_title"]')
            if await commit_title_locator.count() > 0:
                await commit_title_locator.fill("Add GitHub Actions workflow for Mini App auto-deploy")
                print("✅ Commit message заполнен")
            
            print("💾 Сохранение файла...")
            
            # Ищем кнопку Commit
            commit_button = page.locator('button:has-text("Commit new file")')
            if await commit_button.count() > 0:
                await commit_button.click()
                print("✅ Кнопка Commit нажата")
            else:
                # Альтернативные селекторы
                alt_commit = page.locator('input[value*="Commit new file"]')
                if await alt_commit.count() > 0:
                    await alt_commit.click()
                    print("✅ Альтернативная кнопка Commit нажата")
                else:
                    print("❌ Кнопка Commit не найдена")
                    return False
            
            print("⏳ Ожидание сохранения...")
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            # Проверяем результат
            current_url = page.url
            if 'deploy-miniapp.yml' in current_url or ('znakomstva_bot' in current_url and 'new' not in current_url):
                print("✅ Файл успешно создан!")
                
                print("📊 Переход к GitHub Actions...")
                await page.goto("https://github.com/KampeLoL/znakomstva_bot/actions")
                await page.wait_for_load_state('networkidle')
                
                print("🎉 УСПЕХ! Автоматический деплой настроен!")
                print("🔗 Страница Actions открыта")
                
                return True
            else:
                print(f"⚠️ Неожиданный URL: {current_url}")
                print("🔍 Проверьте результат вручную")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            print(f"🔍 Текущая страница: {page.url}")
            
            # Сохраняем скриншот
            try:
                await page.screenshot(path='final_error.png')
                print("📸 Скриншот сохранен: final_error.png")
            except:
                pass
            
            return False
        
        finally:
            print("\n" + "="*50)
            print("🔍 Браузер остается открытым для проверки результата")
            print("📊 Проверьте GitHub Actions в открытой вкладке")
            print("⏳ Нажмите Enter для закрытия браузера...")
            input()
            await browser.close()

async def main():
    print("🎭 ФИНАЛЬНЫЙ PLAYWRIGHT ЗАГРУЗЧИК")
    print("=" * 45)
    print("🔧 Без JavaScript evaluate - только DOM")
    print("🔐 Автоматический вход с 2FA поддержкой")
    print("📁 Создание .github/workflows/deploy-miniapp.yml")
    print("🚀 Настройка автоматического деплоя")
    
    choice = input("\n❓ Запустить финальную версию? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        print("\n🚀 Запускаю финальную автоматизацию...")
        success = await final_github_upload()
        
        if success:
            print("\n🎉 ПОЗДРАВЛЯЮ! ВСЁ ГОТОВО!")
            print("=" * 45)
            print("✅ Workflow файл загружен в GitHub")
            print("✅ GitHub Actions активирован")
            print("✅ Автоматический деплой настроен")
            print("✅ Netlify интеграция работает")
            
            print("\n🧪 ТЕСТИРОВАНИЕ:")
            print("1. Измените файл deploy/index.html")
            print("2. Сделайте commit и push")
            print("3. Проверьте Actions - деплой запустится автоматически!")
            print("4. Mini App обновится на https://fluffy-crumble-05e0fb.netlify.app")
            
            print("\n🔗 Полезные ссылки:")
            print("📊 Actions: https://github.com/KampeLoL/znakomstva_bot/actions")
            print("📱 Mini App: https://fluffy-crumble-05e0fb.netlify.app")
            print("🔐 Secrets: https://github.com/KampeLoL/znakomstva_bot/settings/secrets/actions")
            
        else:
            print("\n❌ Возникла проблема")
            print("📋 Варианты решения:")
            print("1. Проверьте скриншот: final_error.png")
            print("2. Попробуйте запустить скрипт еще раз")
            print("3. Загрузите файл вручную через веб-интерфейс")
            print("4. Проверьте, что вы вошли в правильный аккаунт")
    else:
        print("\n📋 Для ручной загрузки:")
        print("1. https://github.com/KampeLoL/znakomstva_bot")
        print("2. Add file → Create new file")
        print("3. Имя: .github/workflows/deploy-miniapp.yml")
        print("4. Содержимое: скопировать из локального файла")
        print("5. Commit new file")

if __name__ == "__main__":
    asyncio.run(main())
