{"name": "dating-bot-miniapp", "version": "1.0.0", "description": "Полнофункциональное Mini App для Telegram бота знакомств", "main": "index.html", "scripts": {"start": "python -m http.server 8000", "build": "echo 'Building Mini App...' && echo 'Build completed!'", "test": "echo 'Testing HTML structure...' && node test.js", "deploy": "netlify deploy --prod --dir .", "dev": "python -m http.server 8000"}, "keywords": ["telegram", "miniapp", "dating", "bot", "swipe", "javascript"], "author": "Dating Bot Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/USERNAME/dating-bot-miniapp.git"}, "bugs": {"url": "https://github.com/USERNAME/dating-bot-miniapp/issues"}, "homepage": "https://USERNAME.github.io/dating-bot-miniapp", "devDependencies": {"netlify-cli": "^17.0.0"}, "engines": {"node": ">=18.0.0"}}