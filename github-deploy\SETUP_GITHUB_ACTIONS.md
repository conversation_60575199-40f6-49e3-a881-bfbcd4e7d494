# 🚀 Настройка автоматического деплоя через GitHub Actions

## 📋 Пошаговая инструкция

### 1. **Создание GitHub репозитория**

1. Откройте https://github.com/new
2. Название: `dating-bot-miniapp`
3. Описание: `Mini App для Telegram бота знакомств`
4. Публичный репозиторий
5. Инициализируйте с README
6. Нажмите **Create repository**

### 2. **Загрузка файлов**

1. Скачайте все файлы из папки `github-deploy/`
2. Загрузите их в корень вашего репозитория:
   - `index.html` (основное приложение)
   - `.github/workflows/deploy.yml` (GitHub Actions)
   - `package.json` (конфигурация)
   - `README.md` (документация)
   - `.gitignore` (игнорируемые файлы)

### 3. **Получение Netlify токенов**

#### 3.1 Получение NETLIFY_AUTH_TOKEN:
1. Откройте https://app.netlify.com/user/applications
2. Нажмите **New access token**
3. Название: `GitHub Actions Deploy`
4. Скопируйте токен (сохраните его!)

#### 3.2 Получение NETLIFY_SITE_ID:
1. Откройте ваш сайт на Netlify
2. Перейдите в **Site settings**
3. Скопируйте **Site ID** (например: `fluffy-crumble-05e0fb`)

### 4. **Настройка GitHub Secrets**

1. Откройте ваш репозиторий на GitHub
2. Перейдите в **Settings** → **Secrets and variables** → **Actions**
3. Нажмите **New repository secret**
4. Добавьте два секрета:

**Секрет 1:**
- Name: `NETLIFY_AUTH_TOKEN`
- Secret: ваш токен из пункта 3.1

**Секрет 2:**
- Name: `NETLIFY_SITE_ID`
- Secret: ваш Site ID из пункта 3.2

### 5. **Активация GitHub Actions**

1. Перейдите в **Actions** в вашем репозитории
2. Если нужно, нажмите **I understand my workflows, go ahead and enable them**
3. Workflow `🚀 Deploy Mini App to Netlify` должен появиться

### 6. **Первый деплой**

1. Внесите любое изменение в `index.html`
2. Сделайте commit и push в main ветку
3. Перейдите в **Actions** и наблюдайте за процессом деплоя
4. После успешного деплоя ваш Mini App обновится автоматически

## 🔄 Автоматический процесс

После настройки при каждом push в main ветку:

1. ✅ **GitHub Actions запускается**
2. ✅ **Проверяет HTML структуру**
3. ✅ **Деплоит на Netlify**
4. ✅ **Обновляет Mini App**
5. ✅ **Отправляет уведомления**

## 🛠️ Ручной деплой

Если нужно запустить деплой вручную:

1. Перейдите в **Actions**
2. Выберите workflow `🚀 Deploy Mini App to Netlify`
3. Нажмите **Run workflow**
4. Выберите ветку и нажмите **Run workflow**

## 🔧 Обновление URL в боте

После первого деплоя обновите URL в вашем боте:

```python
# В файле main.py
markup.add(types.InlineKeyboardButton(
    "📱 Открыть Mini App", 
    web_app=types.WebAppInfo(url="https://ВАШ_SITE_ID.netlify.app")
))
```

## 🎯 Тестирование

1. Откройте https://ВАШ_SITE_ID.netlify.app в браузере
2. Проверьте работу Mini App
3. Протестируйте в Telegram боте
4. Внесите изменения и убедитесь в автоматическом обновлении

## 🚨 Решение проблем

### Ошибка: "NETLIFY_AUTH_TOKEN not found"
- Проверьте правильность названия секрета
- Убедитесь, что токен действителен

### Ошибка: "NETLIFY_SITE_ID not found"
- Проверьте правильность Site ID
- Убедитесь, что сайт существует на Netlify

### Деплой не запускается
- Проверьте, что файл `.github/workflows/deploy.yml` в правильной папке
- Убедитесь, что GitHub Actions включены

---

**После настройки у вас будет полностью автоматизированный процесс деплоя!** 🎉
