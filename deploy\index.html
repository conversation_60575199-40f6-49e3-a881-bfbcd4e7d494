<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Знакомства - Mini App</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <style>
        /* Основные стили */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
            user-select: none;
        }

        #app {
            height: 100vh;
            position: relative;
        }

        /* Экраны */
        .screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .screen.active {
            opacity: 1;
            visibility: visible;
        }

        /* Загрузочный экран */
        #loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Главный экран */
        #main {
            background: #f8f9fa;
            padding-bottom: 80px;
        }

        /* Заголовок */
        .header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }

        .settings-btn, .edit-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .settings-btn:hover, .edit-btn:hover {
            background: rgba(0,0,0,0.1);
        }

        /* Контентные экраны */
        .content-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            transform: translateX(20px);
        }

        .content-screen.active {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        /* Контейнер карточек */
        .cards-container {
            position: relative;
            height: calc(100vh - 200px);
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Карточка анкеты */
        .profile-card {
            position: absolute;
            width: 90%;
            max-width: 350px;
            height: 70%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            cursor: grab;
            transition: transform 0.3s ease;
        }

        .profile-card:active {
            cursor: grabbing;
        }

        .profile-card.dragging {
            transition: none;
        }

        .card-image {
            width: 100%;
            height: 60%;
            object-fit: cover;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
        }

        .card-content {
            padding: 20px;
            height: 40%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .card-name {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .card-info {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .card-description {
            font-size: 14px;
            color: #888;
            line-height: 1.4;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        /* Кнопки действий */
        .action-buttons {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 30px;
            z-index: 10;
        }

        .action-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transition: all 0.2s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .dislike-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .like-btn {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        /* Нижняя навигация */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-btn {
            background: none;
            border: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 10px;
            min-width: 60px;
        }

        .nav-btn.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 12px;
            font-weight: 500;
        }

        /* Сообщение об отсутствии карточек */
        .no-more-cards {
            text-align: center;
            padding: 40px 20px;
        }

        .no-more-cards.hidden {
            display: none;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            transition: transform 0.2s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.05);
        }

        /* Списки */
        .matches-list, .stats-content, .profile-content {
            padding: 20px;
        }

        .match-item {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .match-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-right: 15px;
        }

        .match-info h3 {
            margin-bottom: 5px;
            color: #333;
        }

        .match-info p {
            color: #666;
            font-size: 14px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        /* Адаптивность */
        @media (max-width: 480px) {
            .cards-container {
                padding: 10px;
            }

            .profile-card {
                width: 95%;
            }

            .action-buttons {
                gap: 20px;
            }

            .action-btn {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Загрузочный экран -->
        <div id="loading" class="screen active">
            <div class="loading-spinner"></div>
            <h2>Загрузка...</h2>
        </div>

        <!-- Главный экран с навигацией -->
        <div id="main" class="screen">
            <!-- Навигационная панель -->
            <nav class="bottom-nav">
                <button class="nav-btn active" data-screen="discover">
                    <span class="nav-icon">🔥</span>
                    <span class="nav-label">Анкеты</span>
                </button>
                <button class="nav-btn" data-screen="matches">
                    <span class="nav-icon">💕</span>
                    <span class="nav-label">Совпадения</span>
                </button>
                <button class="nav-btn" data-screen="profile">
                    <span class="nav-icon">👤</span>
                    <span class="nav-label">Профиль</span>
                </button>
                <button class="nav-btn" data-screen="stats">
                    <span class="nav-icon">📊</span>
                    <span class="nav-label">Статистика</span>
                </button>
            </nav>

            <!-- Экран просмотра анкет -->
            <div id="discover" class="content-screen active">
                <div class="header">
                    <h1>🔥 Знакомства</h1>
                    <button id="settingsBtn" class="settings-btn">⚙️</button>
                </div>

                <div class="cards-container">
                    <div id="noMoreCards" class="no-more-cards hidden">
                        <h3>😔 Анкеты закончились</h3>
                        <p>Заходите позже или пригласите друзей!</p>
                        <button id="refreshBtn" class="refresh-btn">🔄 Обновить</button>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="dislikeBtn" class="action-btn dislike-btn">
                        <span class="btn-icon">👎</span>
                    </button>
                    <button id="likeBtn" class="action-btn like-btn">
                        <span class="btn-icon">❤️</span>
                    </button>
                </div>
            </div>

            <!-- Экран совпадений -->
            <div id="matches" class="content-screen">
                <div class="header">
                    <h1>💕 Совпадения</h1>
                </div>
                <div id="matchesList" class="matches-list">
                    <!-- Список совпадений будет загружен динамически -->
                </div>
            </div>

            <!-- Экран профиля -->
            <div id="profile" class="content-screen">
                <div class="header">
                    <h1>👤 Мой профиль</h1>
                </div>
                <div id="profileContent" class="profile-content">
                    <!-- Содержимое профиля будет загружено динамически -->
                </div>
            </div>

            <!-- Экран статистики -->
            <div id="stats" class="content-screen">
                <div class="header">
                    <h1>📊 Статистика</h1>
                </div>
                <div id="statsContent" class="stats-content">
                    <!-- Статистика будет загружена динамически -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Telegram Web App API
        const tg = window.Telegram.WebApp;

        // Глобальные переменные
        let currentUser = null;
        let currentProfiles = [];
        let currentProfileIndex = 0;
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let currentX = 0;
        let currentY = 0;

        // Мок-данные для демонстрации
        const MOCK_PROFILES = [
            {
                id: 1,
                name: 'Анна',
                age: 25,
                city: 'Москва',
                description: 'Люблю путешествовать и читать книги. Ищу интересного собеседника для серьезных отношений.',
                photo: null
            },
            {
                id: 2,
                name: 'Мария',
                age: 23,
                city: 'Санкт-Петербург',
                description: 'Фотограф и художник. Обожаю кофе и долгие прогулки по городу.',
                photo: null
            },
            {
                id: 3,
                name: 'Елена',
                age: 27,
                city: 'Екатеринбург',
                description: 'Работаю в IT, увлекаюсь спортом и готовкой. Ищу того, с кем можно строить планы на будущее.',
                photo: null
            },
            {
                id: 4,
                name: 'Ольга',
                age: 24,
                city: 'Казань',
                description: 'Учитель английского языка. Люблю активный отдых и изучение новых культур.',
                photo: null
            },
            {
                id: 5,
                name: 'Дарья',
                age: 26,
                city: 'Новосибирск',
                description: 'Дизайнер интерьеров. Ценю красоту во всем и ищу родственную душу.',
                photo: null
            }
        ];

        const MOCK_MATCHES = [
            { id: 1, name: 'Анна', age: 25, city: 'Москва', photo: null },
            { id: 2, name: 'Мария', age: 23, city: 'Санкт-Петербург', photo: null }
        ];

        const MOCK_STATS = {
            likes_sent: 15,
            likes_received: 8,
            matches_count: 3,
            dislikes_sent: 12
        };

        // Инициализация приложения
        document.addEventListener('DOMContentLoaded', function() {
            initTelegramWebApp();
            initNavigation();
            initSwipeGestures();

            // Симуляция загрузки
            setTimeout(() => {
                showScreen('main');
                loadProfiles();
            }, 2000);
        });

        // Инициализация Telegram Web App
        function initTelegramWebApp() {
            tg.ready();
            tg.expand();

            // Настройка темы
            document.body.style.backgroundColor = tg.backgroundColor || '#f8f9fa';

            // Получение данных пользователя
            if (tg.initDataUnsafe && tg.initDataUnsafe.user) {
                currentUser = tg.initDataUnsafe.user;
                console.log('Пользователь:', currentUser);
            }
        }

        // Навигация между экранами
        function initNavigation() {
            const navButtons = document.querySelectorAll('.nav-btn');

            navButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    const screenName = btn.dataset.screen;
                    switchContentScreen(screenName);

                    // Обновление активной кнопки
                    navButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                });
            });
        }

        // Переключение контентных экранов
        function switchContentScreen(screenName) {
            const screens = document.querySelectorAll('.content-screen');
            screens.forEach(screen => screen.classList.remove('active'));

            const targetScreen = document.getElementById(screenName);
            if (targetScreen) {
                targetScreen.classList.add('active');

                // Загрузка данных для экрана
                switch(screenName) {
                    case 'discover':
                        loadProfiles();
                        break;
                    case 'matches':
                        loadMatches();
                        break;
                    case 'profile':
                        loadProfile();
                        break;
                    case 'stats':
                        loadStats();
                        break;
                }
            }
        }

        // Показ экрана
        function showScreen(screenId) {
            const screens = document.querySelectorAll('.screen');
            screens.forEach(screen => screen.classList.remove('active'));

            const targetScreen = document.getElementById(screenId);
            if (targetScreen) {
                targetScreen.classList.add('active');
            }
        }

        // Загрузка профилей для просмотра
        function loadProfiles() {
            currentProfiles = [...MOCK_PROFILES];
            currentProfileIndex = 0;
            displayCurrentProfile();
        }

        // Отображение текущего профиля
        function displayCurrentProfile() {
            const container = document.querySelector('.cards-container');
            const noMoreCards = document.getElementById('noMoreCards');

            if (currentProfileIndex >= currentProfiles.length) {
                showNoMoreCards();
                return;
            }

            noMoreCards.classList.add('hidden');

            // Удаляем старые карточки
            const oldCards = container.querySelectorAll('.profile-card');
            oldCards.forEach(card => card.remove());

            // Создаем новую карточку
            const profile = currentProfiles[currentProfileIndex];
            const card = createProfileCard(profile);
            container.appendChild(card);
        }

        // Создание карточки профиля
        function createProfileCard(profile) {
            const card = document.createElement('div');
            card.className = 'profile-card';
            card.dataset.profileId = profile.id;

            card.innerHTML = `
                <img src="${profile.photo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7QpNC+0YLQvjwvdGV4dD48L3N2Zz4='}" alt="${profile.name}" class="card-image">
                <div class="card-content">
                    <div class="card-name">${profile.name}, ${profile.age}</div>
                    <div class="card-info">📍 ${profile.city}</div>
                    <div class="card-description">${profile.description}</div>
                </div>
            `;

            return card;
        }

        // Показ сообщения об отсутствии карточек
        function showNoMoreCards() {
            const noMoreCards = document.getElementById('noMoreCards');
            noMoreCards.classList.remove('hidden');

            // Удаляем все карточки
            const container = document.querySelector('.cards-container');
            const cards = container.querySelectorAll('.profile-card');
            cards.forEach(card => card.remove());
        }

        // Инициализация свайп-жестов
        function initSwipeGestures() {
            const container = document.querySelector('.cards-container');
            const likeBtn = document.getElementById('likeBtn');
            const dislikeBtn = document.getElementById('dislikeBtn');
            const refreshBtn = document.getElementById('refreshBtn');

            // Обработчики кнопок
            likeBtn.addEventListener('click', () => handleReaction('like'));
            dislikeBtn.addEventListener('click', () => handleReaction('dislike'));
            refreshBtn.addEventListener('click', () => {
                currentProfileIndex = 0;
                loadProfiles();
            });

            // Touch события для свайпа
            container.addEventListener('touchstart', handleTouchStart, { passive: false });
            container.addEventListener('touchmove', handleTouchMove, { passive: false });
            container.addEventListener('touchend', handleTouchEnd, { passive: false });

            // Mouse события для десктопа
            container.addEventListener('mousedown', handleMouseDown);
            container.addEventListener('mousemove', handleMouseMove);
            container.addEventListener('mouseup', handleMouseUp);
            container.addEventListener('mouseleave', handleMouseUp);
        }

        // Обработка реакции (лайк/дизлайк)
        function handleReaction(reaction) {
            if (currentProfileIndex >= currentProfiles.length) return;

            const profile = currentProfiles[currentProfileIndex];

            // Анимация удаления карточки
            const card = document.querySelector('.profile-card');
            if (card) {
                card.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
                const direction = reaction === 'like' ? 1 : -1;
                card.style.transform = `translateX(${direction * 300}px) rotate(${direction * 30}deg)`;
                card.style.opacity = '0';

                setTimeout(() => {
                    card.remove();
                    currentProfileIndex++;
                    displayCurrentProfile();
                }, 300);
            }

            // Показываем уведомление
            if (reaction === 'like') {
                // Симулируем случайное совпадение
                const isMatch = Math.random() < 0.3;
                if (isMatch) {
                    showNotification('🎉 Взаимная симпатия!');
                    // Добавляем в совпадения
                    MOCK_MATCHES.push({
                        id: profile.id,
                        name: profile.name,
                        age: profile.age,
                        city: profile.city,
                        photo: profile.photo
                    });
                } else {
                    showNotification('❤️ Лайк отправлен!');
                }
            } else {
                showNotification('👎 Анкета пропущена');
            }
        }

        // Показ уведомления
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 20px;
                z-index: 3000;
                font-size: 14px;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => notification.style.opacity = '1', 100);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 2000);
        }

        // Загрузка совпадений
        function loadMatches() {
            const matchesList = document.getElementById('matchesList');

            if (MOCK_MATCHES.length === 0) {
                matchesList.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <h3>💔 Пока нет совпадений</h3>
                        <p>Продолжайте ставить лайки!</p>
                    </div>
                `;
                return;
            }

            matchesList.innerHTML = MOCK_MATCHES.map(match => `
                <div class="match-item">
                    <div class="match-avatar"></div>
                    <div class="match-info">
                        <h3>${match.name}, ${match.age}</h3>
                        <p>📍 ${match.city}</p>
                    </div>
                </div>
            `).join('');
        }

        // Загрузка профиля пользователя
        function loadProfile() {
            const profileContent = document.getElementById('profileContent');

            const mockProfile = {
                name: currentUser?.first_name || 'Пользователь',
                age: 25,
                city: 'Москва',
                description: 'Расскажите о себе...',
                photo: null
            };

            profileContent.innerHTML = `
                <div class="profile-card" style="position: static; width: 100%; height: auto; margin-bottom: 20px;">
                    <img src="${mockProfile.photo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7QpNC+0YLQvjwvdGV4dD48L3N2Zz4='}" alt="${mockProfile.name}" class="card-image">
                    <div class="card-content">
                        <div class="card-name">${mockProfile.name}, ${mockProfile.age}</div>
                        <div class="card-info">📍 ${mockProfile.city}</div>
                        <div class="card-description">${mockProfile.description}</div>
                    </div>
                </div>
            `;
        }

        // Загрузка статистики
        function loadStats() {
            const statsContent = document.getElementById('statsContent');

            statsContent.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${MOCK_STATS.likes_received}</div>
                    <div class="stat-label">Лайков получено</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${MOCK_STATS.likes_sent}</div>
                    <div class="stat-label">Лайков отправлено</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${MOCK_STATS.matches_count}</div>
                    <div class="stat-label">Взаимных симпатий</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${MOCK_STATS.dislikes_sent}</div>
                    <div class="stat-label">Анкет пропущено</div>
                </div>
            `;
        }

        // Touch обработчики (упрощенные для демо)
        function handleTouchStart(e) {
            const card = e.target.closest('.profile-card');
            if (!card) return;

            isDragging = true;
            startX = e.touches[0].clientX;
            card.classList.add('dragging');
        }

        function handleTouchMove(e) {
            if (!isDragging) return;
            e.preventDefault();

            const card = e.target.closest('.profile-card');
            if (!card) return;

            currentX = e.touches[0].clientX - startX;
            updateCardPosition(card, currentX, 0);
        }

        function handleTouchEnd(e) {
            if (!isDragging) return;

            const card = e.target.closest('.profile-card');
            if (!card) return;

            isDragging = false;
            card.classList.remove('dragging');

            const threshold = 100;
            if (Math.abs(currentX) > threshold) {
                if (currentX > 0) {
                    handleReaction('like');
                } else {
                    handleReaction('dislike');
                }
            } else {
                card.style.transform = '';
                card.style.opacity = '';
            }

            currentX = 0;
        }

        // Mouse обработчики (аналогично touch)
        function handleMouseDown(e) {
            const card = e.target.closest('.profile-card');
            if (!card) return;

            isDragging = true;
            startX = e.clientX;
            card.classList.add('dragging');
        }

        function handleMouseMove(e) {
            if (!isDragging) return;

            const card = e.target.closest('.profile-card');
            if (!card) return;

            currentX = e.clientX - startX;
            updateCardPosition(card, currentX, 0);
        }

        function handleMouseUp(e) {
            if (!isDragging) return;

            const card = document.querySelector('.profile-card.dragging');
            if (!card) return;

            isDragging = false;
            card.classList.remove('dragging');

            const threshold = 100;
            if (Math.abs(currentX) > threshold) {
                if (currentX > 0) {
                    handleReaction('like');
                } else {
                    handleReaction('dislike');
                }
            } else {
                card.style.transform = '';
                card.style.opacity = '';
            }

            currentX = 0;
        }

        // Обновление позиции карточки
        function updateCardPosition(card, x, y) {
            const rotation = x * 0.1;
            card.style.transform = `translate(${x}px, ${y}px) rotate(${rotation}deg)`;

            const opacity = Math.max(0.5, 1 - Math.abs(x) / 200);
            card.style.opacity = opacity;
        }
    </script>
</body>
</html>