// Standalone версия Mini App для GitHub Pages (без серверного API)
const tg = window.Telegram.WebApp;

// Глобальные переменные
let currentUser = null;
let currentProfiles = [];
let currentProfileIndex = 0;
let isDragging = false;
let startX = 0;
let startY = 0;
let currentX = 0;
let currentY = 0;

// Мок-данные для демонстрации
const MOCK_PROFILES = [
    {
        id: 1,
        name: 'Ан<PERSON>',
        age: 25,
        city: 'Москва',
        description: 'Люблю путешествовать и читать книги. Ищу интересного собеседника для серьезных отношений.',
        photo: null
    },
    {
        id: 2,
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        age: 23,
        city: 'Санкт-Петербург',
        description: 'Фотограф и художник. Обожаю кофе и долгие прогулки по городу.',
        photo: null
    },
    {
        id: 3,
        name: 'Елена',
        age: 27,
        city: 'Екатеринбург',
        description: 'Работаю в IT, увлекаюсь спортом и готовкой. Ищу того, с кем можно строить планы на будущее.',
        photo: null
    },
    {
        id: 4,
        name: 'Ольга',
        age: 24,
        city: 'Казань',
        description: 'Учитель английского языка. Люблю активный отдых и изучение новых культур.',
        photo: null
    },
    {
        id: 5,
        name: 'Дарья',
        age: 26,
        city: 'Новосибирск',
        description: 'Дизайнер интерьеров. Ценю красоту во всем и ищу родственную душу.',
        photo: null
    }
];

const MOCK_MATCHES = [
    { id: 1, name: 'Анна', age: 25, city: 'Москва', photo: null },
    { id: 2, name: 'Мария', age: 23, city: 'Санкт-Петербург', photo: null }
];

const MOCK_STATS = {
    likes_sent: 15,
    likes_received: 8,
    matches_count: 3,
    dislikes_sent: 12
};

// Инициализация приложения
document.addEventListener('DOMContentLoaded', function() {
    initTelegramWebApp();
    initNavigation();
    initSwipeGestures();
    initModals();
    
    // Симуляция загрузки
    setTimeout(() => {
        showScreen('main');
        loadProfiles();
    }, 2000);
});

// Инициализация Telegram Web App
function initTelegramWebApp() {
    tg.ready();
    tg.expand();
    
    // Настройка темы
    document.body.style.backgroundColor = tg.backgroundColor || '#f8f9fa';
    
    // Получение данных пользователя
    if (tg.initDataUnsafe && tg.initDataUnsafe.user) {
        currentUser = tg.initDataUnsafe.user;
        console.log('Пользователь:', currentUser);
    }
}

// Навигация между экранами
function initNavigation() {
    const navButtons = document.querySelectorAll('.nav-btn');
    
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const screenName = btn.dataset.screen;
            switchContentScreen(screenName);
            
            // Обновление активной кнопки
            navButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });
}

// Переключение контентных экранов
function switchContentScreen(screenName) {
    const screens = document.querySelectorAll('.content-screen');
    screens.forEach(screen => screen.classList.remove('active'));
    
    const targetScreen = document.getElementById(screenName);
    if (targetScreen) {
        targetScreen.classList.add('active');
        
        // Загрузка данных для экрана
        switch(screenName) {
            case 'discover':
                loadProfiles();
                break;
            case 'matches':
                loadMatches();
                break;
            case 'profile':
                loadProfile();
                break;
            case 'stats':
                loadStats();
                break;
        }
    }
}

// Показ экрана
function showScreen(screenId) {
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => screen.classList.remove('active'));
    
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
        targetScreen.classList.add('active');
    }
}

// Загрузка профилей для просмотра
function loadProfiles() {
    currentProfiles = [...MOCK_PROFILES];
    currentProfileIndex = 0;
    displayCurrentProfile();
}

// Отображение текущего профиля
function displayCurrentProfile() {
    const container = document.querySelector('.cards-container');
    const noMoreCards = document.getElementById('noMoreCards');
    
    if (currentProfileIndex >= currentProfiles.length) {
        showNoMoreCards();
        return;
    }
    
    noMoreCards.classList.add('hidden');
    
    // Удаляем старые карточки
    const oldCards = container.querySelectorAll('.profile-card');
    oldCards.forEach(card => card.remove());
    
    // Создаем новую карточку
    const profile = currentProfiles[currentProfileIndex];
    const card = createProfileCard(profile);
    container.appendChild(card);
}

// Создание карточки профиля
function createProfileCard(profile) {
    const card = document.createElement('div');
    card.className = 'profile-card';
    card.dataset.profileId = profile.id;
    
    card.innerHTML = `
        <img src="${profile.photo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7QpNC+0YLQvjwvdGV4dD48L3N2Zz4='}" alt="${profile.name}" class="card-image">
        <div class="card-content">
            <div class="card-name">${profile.name}, ${profile.age}</div>
            <div class="card-info">📍 ${profile.city}</div>
            <div class="card-description">${profile.description}</div>
        </div>
    `;
    
    return card;
}

// Показ сообщения об отсутствии карточек
function showNoMoreCards() {
    const noMoreCards = document.getElementById('noMoreCards');
    noMoreCards.classList.remove('hidden');
    
    // Удаляем все карточки
    const container = document.querySelector('.cards-container');
    const cards = container.querySelectorAll('.profile-card');
    cards.forEach(card => card.remove());
}

// Инициализация свайп-жестов
function initSwipeGestures() {
    const container = document.querySelector('.cards-container');
    const likeBtn = document.getElementById('likeBtn');
    const dislikeBtn = document.getElementById('dislikeBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    
    // Обработчики кнопок
    likeBtn.addEventListener('click', () => handleReaction('like'));
    dislikeBtn.addEventListener('click', () => handleReaction('dislike'));
    refreshBtn.addEventListener('click', () => {
        currentProfileIndex = 0;
        loadProfiles();
    });
    
    // Touch события для свайпа
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });
    
    // Mouse события для десктопа
    container.addEventListener('mousedown', handleMouseDown);
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseup', handleMouseUp);
    container.addEventListener('mouseleave', handleMouseUp);
}

// Обработка начала касания
function handleTouchStart(e) {
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    isDragging = true;
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
    card.classList.add('dragging');
}

// Обработка движения касания
function handleTouchMove(e) {
    if (!isDragging) return;
    e.preventDefault();
    
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    currentX = e.touches[0].clientX - startX;
    currentY = e.touches[0].clientY - startY;
    
    updateCardPosition(card, currentX, currentY);
}

// Обработка окончания касания
function handleTouchEnd(e) {
    if (!isDragging) return;
    
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    isDragging = false;
    card.classList.remove('dragging');
    
    // Определяем направление свайпа
    const threshold = 100;
    if (Math.abs(currentX) > threshold) {
        if (currentX > 0) {
            handleReaction('like');
        } else {
            handleReaction('dislike');
        }
    } else {
        // Возвращаем карточку в исходное положение
        card.style.transform = '';
        card.style.opacity = '';
    }
    
    currentX = 0;
    currentY = 0;
}

// Аналогичные обработчики для мыши
function handleMouseDown(e) {
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    isDragging = true;
    startX = e.clientX;
    startY = e.clientY;
    card.classList.add('dragging');
}

function handleMouseMove(e) {
    if (!isDragging) return;
    
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    currentX = e.clientX - startX;
    currentY = e.clientY - startY;
    
    updateCardPosition(card, currentX, currentY);
}

function handleMouseUp(e) {
    if (!isDragging) return;
    
    const card = document.querySelector('.profile-card.dragging');
    if (!card) return;
    
    isDragging = false;
    card.classList.remove('dragging');
    
    const threshold = 100;
    if (Math.abs(currentX) > threshold) {
        if (currentX > 0) {
            handleReaction('like');
        } else {
            handleReaction('dislike');
        }
    } else {
        card.style.transform = '';
        card.style.opacity = '';
    }
    
    currentX = 0;
    currentY = 0;
}

// Обновление позиции карточки
function updateCardPosition(card, x, y) {
    const rotation = x * 0.1;
    card.style.transform = `translate(${x}px, ${y}px) rotate(${rotation}deg)`;
    
    // Добавляем визуальную обратную связь
    const opacity = Math.max(0.5, 1 - Math.abs(x) / 200);
    card.style.opacity = opacity;
}

// Обработка реакции (лайк/дизлайк)
function handleReaction(reaction) {
    if (currentProfileIndex >= currentProfiles.length) return;
    
    const profile = currentProfiles[currentProfileIndex];
    
    // Анимация удаления карточки
    const card = document.querySelector('.profile-card');
    if (card) {
        card.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
        const direction = reaction === 'like' ? 1 : -1;
        card.style.transform = `translateX(${direction * 300}px) rotate(${direction * 30}deg)`;
        card.style.opacity = '0';
        
        setTimeout(() => {
            card.remove();
            currentProfileIndex++;
            displayCurrentProfile();
        }, 300);
    }
    
    // Показываем уведомление
    if (reaction === 'like') {
        // Симулируем случайное совпадение
        const isMatch = Math.random() < 0.3;
        if (isMatch) {
            showNotification('🎉 Взаимная симпатия!');
            // Добавляем в совпадения
            MOCK_MATCHES.push({
                id: profile.id,
                name: profile.name,
                age: profile.age,
                city: profile.city,
                photo: profile.photo
            });
        } else {
            showNotification('❤️ Лайк отправлен!');
        }
    } else {
        showNotification('👎 Анкета пропущена');
    }
}

// Показ уведомления
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        z-index: 3000;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => notification.style.opacity = '1', 100);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 2000);
}

// Загрузка совпадений
function loadMatches() {
    const matchesList = document.getElementById('matchesList');
    
    if (MOCK_MATCHES.length === 0) {
        matchesList.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <h3>💔 Пока нет совпадений</h3>
                <p>Продолжайте ставить лайки!</p>
            </div>
        `;
        return;
    }
    
    matchesList.innerHTML = MOCK_MATCHES.map(match => `
        <div class="match-item">
            <div class="match-avatar"></div>
            <div class="match-info">
                <h3>${match.name}, ${match.age}</h3>
                <p>📍 ${match.city}</p>
            </div>
        </div>
    `).join('');
}

// Загрузка профиля пользователя
function loadProfile() {
    const profileContent = document.getElementById('profileContent');
    
    const mockProfile = {
        name: currentUser?.first_name || 'Пользователь',
        age: 25,
        city: 'Москва',
        description: 'Расскажите о себе...',
        photo: null
    };
    
    profileContent.innerHTML = `
        <div class="profile-card" style="position: static; width: 100%; height: auto; margin-bottom: 20px;">
            <img src="${mockProfile.photo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7QpNC+0YLQvjwvdGV4dD48L3N2Zz4='}" alt="${mockProfile.name}" class="card-image">
            <div class="card-content">
                <div class="card-name">${mockProfile.name}, ${mockProfile.age}</div>
                <div class="card-info">📍 ${mockProfile.city}</div>
                <div class="card-description">${mockProfile.description}</div>
            </div>
        </div>
    `;
}

// Загрузка статистики
function loadStats() {
    const statsContent = document.getElementById('statsContent');
    
    statsContent.innerHTML = `
        <div class="stat-card">
            <div class="stat-number">${MOCK_STATS.likes_received}</div>
            <div class="stat-label">Лайков получено</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${MOCK_STATS.likes_sent}</div>
            <div class="stat-label">Лайков отправлено</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${MOCK_STATS.matches_count}</div>
            <div class="stat-label">Взаимных симпатий</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${MOCK_STATS.dislikes_sent}</div>
            <div class="stat-label">Анкет пропущено</div>
        </div>
    `;
}

// Инициализация модальных окон
function initModals() {
    const editProfileBtn = document.getElementById('editProfileBtn');
    const editModal = document.getElementById('editModal');
    const closeModal = document.getElementById('closeModal');
    const cancelEdit = document.getElementById('cancelEdit');
    const editForm = document.getElementById('editForm');
    
    editProfileBtn.addEventListener('click', () => {
        editModal.classList.add('active');
        loadEditForm();
    });
    
    closeModal.addEventListener('click', () => {
        editModal.classList.remove('active');
    });
    
    cancelEdit.addEventListener('click', () => {
        editModal.classList.remove('active');
    });
    
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) {
            editModal.classList.remove('active');
        }
    });
    
    editForm.addEventListener('submit', handleEditSubmit);
}

// Загрузка данных в форму редактирования
function loadEditForm() {
    document.getElementById('editName').value = currentUser?.first_name || 'Пользователь';
    document.getElementById('editAge').value = 25;
    document.getElementById('editCity').value = 'Москва';
    document.getElementById('editDescription').value = 'Расскажите о себе...';
}

// Обработка отправки формы редактирования
function handleEditSubmit(e) {
    e.preventDefault();
    
    showNotification('✅ Профиль обновлен!');
    document.getElementById('editModal').classList.remove('active');
    loadProfile();
}
