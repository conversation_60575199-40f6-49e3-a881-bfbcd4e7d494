// Telegram Web App API
const tg = window.Telegram.WebApp;

// Глобальные переменные
let currentUser = null;
let currentProfiles = [];
let currentProfileIndex = 0;
let isDragging = false;
let startX = 0;
let startY = 0;
let currentX = 0;
let currentY = 0;

// Инициализация приложения
document.addEventListener('DOMContentLoaded', function() {
    initTelegramWebApp();
    initNavigation();
    initSwipeGestures();
    initModals();
    loadUserData();
    
    // Симуляция загрузки
    setTimeout(() => {
        showScreen('main');
        loadProfiles();
    }, 2000);
});

// Инициализация Telegram Web App
function initTelegramWebApp() {
    tg.ready();
    tg.expand();
    
    // Настройка темы
    document.body.style.backgroundColor = tg.backgroundColor || '#f8f9fa';
    
    // Получение данных пользователя
    if (tg.initDataUnsafe && tg.initDataUnsafe.user) {
        currentUser = tg.initDataUnsafe.user;
        console.log('Пользователь:', currentUser);
    }
}

// Навигация между экранами
function initNavigation() {
    const navButtons = document.querySelectorAll('.nav-btn');
    
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const screenName = btn.dataset.screen;
            switchContentScreen(screenName);
            
            // Обновление активной кнопки
            navButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });
}

// Переключение контентных экранов
function switchContentScreen(screenName) {
    const screens = document.querySelectorAll('.content-screen');
    screens.forEach(screen => screen.classList.remove('active'));
    
    const targetScreen = document.getElementById(screenName);
    if (targetScreen) {
        targetScreen.classList.add('active');
        
        // Загрузка данных для экрана
        switch(screenName) {
            case 'discover':
                loadProfiles();
                break;
            case 'matches':
                loadMatches();
                break;
            case 'profile':
                loadProfile();
                break;
            case 'stats':
                loadStats();
                break;
        }
    }
}

// Показ экрана
function showScreen(screenId) {
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => screen.classList.remove('active'));
    
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
        targetScreen.classList.add('active');
    }
}

// Загрузка профилей для просмотра
async function loadProfiles() {
    try {
        const response = await fetch('/api/profiles', {
            headers: {
                'X-Telegram-Init-Data': tg.initData || ''
            }
        });

        if (!response.ok) {
            throw new Error('Ошибка загрузки профилей');
        }

        const data = await response.json();
        currentProfiles = data.profiles || [];
        currentProfileIndex = 0;
        displayCurrentProfile();

    } catch (error) {
        console.error('Ошибка загрузки профилей:', error);

        // Fallback на мок-данные для тестирования
        const mockProfiles = [
            {
                id: 1,
                name: 'Анна',
                age: 25,
                city: 'Москва',
                description: 'Люблю путешествовать и читать книги. Ищу интересного собеседника для серьезных отношений.',
                photo: null
            },
            {
                id: 2,
                name: 'Мария',
                age: 23,
                city: 'Санкт-Петербург',
                description: 'Фотограф и художник. Обожаю кофе и долгие прогулки по городу.',
                photo: null
            }
        ];

        currentProfiles = mockProfiles;
        currentProfileIndex = 0;
        displayCurrentProfile();
    }
}

// Отображение текущего профиля
function displayCurrentProfile() {
    const container = document.querySelector('.cards-container');
    const noMoreCards = document.getElementById('noMoreCards');
    
    if (currentProfileIndex >= currentProfiles.length) {
        showNoMoreCards();
        return;
    }
    
    noMoreCards.classList.add('hidden');
    
    // Удаляем старые карточки
    const oldCards = container.querySelectorAll('.profile-card');
    oldCards.forEach(card => card.remove());
    
    // Создаем новую карточку
    const profile = currentProfiles[currentProfileIndex];
    const card = createProfileCard(profile);
    container.appendChild(card);
}

// Создание карточки профиля
function createProfileCard(profile) {
    const card = document.createElement('div');
    card.className = 'profile-card';
    card.dataset.profileId = profile.id;
    
    card.innerHTML = `
        <img src="${profile.photo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7QpNC+0YLQvjwvdGV4dD48L3N2Zz4='}" alt="${profile.name}" class="card-image">
        <div class="card-content">
            <div class="card-name">${profile.name}, ${profile.age}</div>
            <div class="card-info">📍 ${profile.city}</div>
            <div class="card-description">${profile.description}</div>
        </div>
    `;
    
    return card;
}

// Показ сообщения об отсутствии карточек
function showNoMoreCards() {
    const noMoreCards = document.getElementById('noMoreCards');
    noMoreCards.classList.remove('hidden');
    
    // Удаляем все карточки
    const container = document.querySelector('.cards-container');
    const cards = container.querySelectorAll('.profile-card');
    cards.forEach(card => card.remove());
}

// Инициализация свайп-жестов
function initSwipeGestures() {
    const container = document.querySelector('.cards-container');
    const likeBtn = document.getElementById('likeBtn');
    const dislikeBtn = document.getElementById('dislikeBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    
    // Обработчики кнопок
    likeBtn.addEventListener('click', () => handleReaction('like'));
    dislikeBtn.addEventListener('click', () => handleReaction('dislike'));
    refreshBtn.addEventListener('click', () => {
        currentProfileIndex = 0;
        loadProfiles();
    });
    
    // Touch события для свайпа
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });
    
    // Mouse события для десктопа
    container.addEventListener('mousedown', handleMouseDown);
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseup', handleMouseUp);
    container.addEventListener('mouseleave', handleMouseUp);
}

// Обработка начала касания
function handleTouchStart(e) {
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    isDragging = true;
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
    card.classList.add('dragging');
}

// Обработка движения касания
function handleTouchMove(e) {
    if (!isDragging) return;
    e.preventDefault();
    
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    currentX = e.touches[0].clientX - startX;
    currentY = e.touches[0].clientY - startY;
    
    updateCardPosition(card, currentX, currentY);
}

// Обработка окончания касания
function handleTouchEnd(e) {
    if (!isDragging) return;
    
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    isDragging = false;
    card.classList.remove('dragging');
    
    // Определяем направление свайпа
    const threshold = 100;
    if (Math.abs(currentX) > threshold) {
        if (currentX > 0) {
            handleReaction('like');
        } else {
            handleReaction('dislike');
        }
    } else {
        // Возвращаем карточку в исходное положение
        card.style.transform = '';
    }
    
    currentX = 0;
    currentY = 0;
}

// Аналогичные обработчики для мыши
function handleMouseDown(e) {
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    isDragging = true;
    startX = e.clientX;
    startY = e.clientY;
    card.classList.add('dragging');
}

function handleMouseMove(e) {
    if (!isDragging) return;
    
    const card = e.target.closest('.profile-card');
    if (!card) return;
    
    currentX = e.clientX - startX;
    currentY = e.clientY - startY;
    
    updateCardPosition(card, currentX, currentY);
}

function handleMouseUp(e) {
    if (!isDragging) return;
    
    const card = document.querySelector('.profile-card.dragging');
    if (!card) return;
    
    isDragging = false;
    card.classList.remove('dragging');
    
    const threshold = 100;
    if (Math.abs(currentX) > threshold) {
        if (currentX > 0) {
            handleReaction('like');
        } else {
            handleReaction('dislike');
        }
    } else {
        card.style.transform = '';
    }
    
    currentX = 0;
    currentY = 0;
}

// Обновление позиции карточки
function updateCardPosition(card, x, y) {
    const rotation = x * 0.1;
    card.style.transform = `translate(${x}px, ${y}px) rotate(${rotation}deg)`;
    
    // Добавляем визуальную обратную связь
    const opacity = Math.max(0.5, 1 - Math.abs(x) / 200);
    card.style.opacity = opacity;
}

// Обработка реакции (лайк/дизлайк)
async function handleReaction(reaction) {
    if (currentProfileIndex >= currentProfiles.length) return;

    const profile = currentProfiles[currentProfileIndex];

    try {
        // Отправляем реакцию на сервер
        const response = await fetch('/api/reaction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Telegram-Init-Data': tg.initData || ''
            },
            body: JSON.stringify({
                target_user_id: profile.id,
                reaction_type: reaction
            })
        });

        if (!response.ok) {
            throw new Error('Ошибка отправки реакции');
        }

        const data = await response.json();

        // Анимация удаления карточки
        const card = document.querySelector('.profile-card');
        if (card) {
            card.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
            const direction = reaction === 'like' ? 1 : -1;
            card.style.transform = `translateX(${direction * 300}px) rotate(${direction * 30}deg)`;
            card.style.opacity = '0';

            setTimeout(() => {
                card.remove();
                currentProfileIndex++;
                displayCurrentProfile();
            }, 300);
        }

        // Показываем уведомление
        if (reaction === 'like') {
            if (data.is_match) {
                showNotification('🎉 Взаимная симпатия!');
                // Можно добавить специальную анимацию для совпадения
            } else {
                showNotification('❤️ Лайк отправлен!');
            }
        } else {
            showNotification('👎 Анкета пропущена');
        }

    } catch (error) {
        console.error('Ошибка отправки реакции:', error);
        showNotification('❌ Ошибка. Попробуйте еще раз.');
    }
}

// Показ уведомления
function showNotification(message) {
    // Создаем элемент уведомления
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        z-index: 3000;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Показываем уведомление
    setTimeout(() => notification.style.opacity = '1', 100);
    
    // Удаляем через 2 секунды
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 2000);
}

// Загрузка совпадений
async function loadMatches() {
    try {
        const matchesList = document.getElementById('matchesList');

        // Мок-данные совпадений
        const mockMatches = [
            { id: 1, name: 'Анна', age: 25, city: 'Москва' },
            { id: 2, name: 'Мария', age: 23, city: 'Санкт-Петербург' }
        ];

        if (mockMatches.length === 0) {
            matchesList.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <h3>💔 Пока нет совпадений</h3>
                    <p>Продолжайте ставить лайки!</p>
                </div>
            `;
            return;
        }

        matchesList.innerHTML = mockMatches.map(match => `
            <div class="match-item">
                <div class="match-avatar"></div>
                <div class="match-info">
                    <h3>${match.name}, ${match.age}</h3>
                    <p>📍 ${match.city}</p>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('Ошибка загрузки совпадений:', error);
    }
}

// Загрузка профиля пользователя
async function loadProfile() {
    try {
        const profileContent = document.getElementById('profileContent');

        // Мок-данные профиля
        const mockProfile = {
            name: currentUser?.first_name || 'Пользователь',
            age: 25,
            city: 'Москва',
            description: 'Расскажите о себе...',
            photo: null
        };

        profileContent.innerHTML = `
            <div class="profile-card" style="position: static; width: 100%; height: auto; margin-bottom: 20px;">
                <img src="${mockProfile.photo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7QpNC+0YLQvjwvdGV4dD48L3N2Zz4='}" alt="${mockProfile.name}" class="card-image">
                <div class="card-content">
                    <div class="card-name">${mockProfile.name}, ${mockProfile.age}</div>
                    <div class="card-info">📍 ${mockProfile.city}</div>
                    <div class="card-description">${mockProfile.description}</div>
                </div>
            </div>
        `;

    } catch (error) {
        console.error('Ошибка загрузки профиля:', error);
    }
}

// Загрузка статистики
async function loadStats() {
    try {
        const statsContent = document.getElementById('statsContent');

        // Мок-данные статистики
        const mockStats = {
            likesReceived: 15,
            likesSent: 23,
            matches: 2,
            profileViews: 45
        };

        statsContent.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${mockStats.likesReceived}</div>
                <div class="stat-label">Лайков получено</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${mockStats.likesSent}</div>
                <div class="stat-label">Лайков отправлено</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${mockStats.matches}</div>
                <div class="stat-label">Взаимных симпатий</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${mockStats.profileViews}</div>
                <div class="stat-label">Просмотров профиля</div>
            </div>
        `;

    } catch (error) {
        console.error('Ошибка загрузки статистики:', error);
    }
}

// Инициализация модальных окон
function initModals() {
    const editProfileBtn = document.getElementById('editProfileBtn');
    const editModal = document.getElementById('editModal');
    const closeModal = document.getElementById('closeModal');
    const cancelEdit = document.getElementById('cancelEdit');
    const editForm = document.getElementById('editForm');

    // Открытие модального окна
    editProfileBtn.addEventListener('click', () => {
        editModal.classList.add('active');
        loadEditForm();
    });

    // Закрытие модального окна
    closeModal.addEventListener('click', () => {
        editModal.classList.remove('active');
    });

    cancelEdit.addEventListener('click', () => {
        editModal.classList.remove('active');
    });

    // Закрытие по клику вне модального окна
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) {
            editModal.classList.remove('active');
        }
    });

    // Обработка формы редактирования
    editForm.addEventListener('submit', handleEditSubmit);
}

// Загрузка данных в форму редактирования
function loadEditForm() {
    // Мок-данные для формы
    document.getElementById('editName').value = currentUser?.first_name || 'Пользователь';
    document.getElementById('editAge').value = 25;
    document.getElementById('editCity').value = 'Москва';
    document.getElementById('editDescription').value = 'Расскажите о себе...';
}

// Обработка отправки формы редактирования
async function handleEditSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const profileData = {
        name: formData.get('name'),
        age: parseInt(formData.get('age')),
        city: formData.get('city'),
        description: formData.get('description')
    };

    try {
        // Здесь будет отправка данных на сервер
        console.log('Сохранение профиля:', profileData);

        showNotification('✅ Профиль обновлен!');
        document.getElementById('editModal').classList.remove('active');

        // Перезагружаем профиль
        loadProfile();

    } catch (error) {
        console.error('Ошибка сохранения профиля:', error);
        showNotification('❌ Ошибка сохранения');
    }
}

// Загрузка данных пользователя
async function loadUserData() {
    try {
        // Здесь будет запрос к серверу для получения данных пользователя
        console.log('Загрузка данных пользователя...');

    } catch (error) {
        console.error('Ошибка загрузки данных пользователя:', error);
    }
}
