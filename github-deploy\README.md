# 🤖 Dating Bot Mini App

Полнофункциональное Mini App для Telegram бота знакомств с автоматическим деплоем на Netlify.

## 🚀 Возможности

- 🔥 **Swipe-интерфейс** для просмотра анкет
- 💕 **Система совпадений** с уведомлениями
- 👤 **Профиль пользователя** с редактированием
- 📊 **Статистика активности**
- 🎨 **Современный дизайн** с анимациями
- 📱 **Адаптивная верстка** для всех устройств

## 🔄 Автоматический деплой

При каждом push в main ветку автоматически:
- ✅ Собирается проект
- ✅ Деплоится на Netlify
- ✅ Обновляется Mini App в боте

## 🛠️ Технологии

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **API**: Telegram Web App API
- **Хостинг**: Netlify
- **CI/CD**: GitHub Actions

## 📱 Демо

**Live Demo**: https://fluffy-crumble-05e0fb.netlify.app

## 🔧 Локальная разработка

```bash
# Клонируйте репозиторий
git clone https://github.com/USERNAME/dating-bot-miniapp.git

# Откройте index.html в браузере
open index.html
```

## 📦 Деплой

Автоматический деплой настроен через GitHub Actions.
При push в main ветку проект автоматически деплоится на Netlify.

### Ручной деплой:

1. Скачайте `index.html`
2. Загрузите на любой статический хостинг
3. Обновите URL в Telegram боте

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature ветку
3. Внесите изменения
4. Создайте Pull Request

## 📄 Лицензия

MIT License - используйте свободно!

---

**Создано с ❤️ для Telegram ботов**
