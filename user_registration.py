from telebot import types
from database import create_user, get_user
from config import MIN_AGE, MAX_AGE, MAX_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_CITY_LENGTH

# Состояния регистрации
class RegistrationState:
    WAITING_NAME = "waiting_name"
    WAITING_AGE = "waiting_age"
    WAITING_CITY = "waiting_city"
    WAITING_DESCRIPTION = "waiting_description"
    WAITING_PHOTO = "waiting_photo"
    CONFIRMING = "confirming"

# Временное хранилище данных регистрации
registration_data = {}

def handle_registration_process(bot, message, user_states):
    """Обработка процесса регистрации"""
    print(f"handle_registration_process вызван для пользователя")
    user_id = message.from_user.id if hasattr(message, 'from_user') else None

    # Если это callback от кнопки "Создать анкету"
    if hasattr(message, 'data') and message.data == "start_registration":
        print(f"Обработка callback start_registration для пользователя {user_id}")
        start_registration(bot, message.chat.id, user_id, user_states)
        return
    
    # Проверяем, находится ли пользователь в процессе регистрации
    if user_id not in user_states or user_states[user_id] != "registration":
        return
    
    # Получаем текущий этап регистрации
    if user_id not in registration_data:
        start_registration(bot, message.chat.id, user_id, user_states)
        return
    
    current_step = registration_data[user_id].get('step', RegistrationState.WAITING_NAME)
    
    if current_step == RegistrationState.WAITING_NAME:
        handle_name_input(bot, message, user_id)
    elif current_step == RegistrationState.WAITING_AGE:
        handle_age_input(bot, message, user_id)
    elif current_step == RegistrationState.WAITING_CITY:
        handle_city_input(bot, message, user_id)
    elif current_step == RegistrationState.WAITING_DESCRIPTION:
        handle_description_input(bot, message, user_id)
    elif current_step == RegistrationState.WAITING_PHOTO:
        handle_photo_input(bot, message, user_id, user_states)

def start_registration(bot, chat_id, user_id, user_states):
    """Начать процесс регистрации"""
    user_states[user_id] = "registration"
    registration_data[user_id] = {
        'step': RegistrationState.WAITING_NAME,
        'data': {}
    }
    
    text = """
📝 Создание анкеты

Давайте создадим вашу анкету для знакомств!

Шаг 1/5: Как вас зовут?
Введите ваше имя (до 50 символов):
    """
    
    bot.send_message(chat_id, text)

def handle_name_input(bot, message, user_id):
    """Обработка ввода имени"""
    name = message.text.strip()
    
    if not name:
        bot.send_message(message.chat.id, "❌ Имя не может быть пустым. Попробуйте еще раз:")
        return
    
    if len(name) > MAX_NAME_LENGTH:
        bot.send_message(message.chat.id, f"❌ Имя слишком длинное (максимум {MAX_NAME_LENGTH} символов). Попробуйте еще раз:")
        return
    
    registration_data[user_id]['data']['name'] = name
    registration_data[user_id]['step'] = RegistrationState.WAITING_AGE
    
    text = f"""
✅ Отлично! Приятно познакомиться, {name}!

Шаг 2/5: Сколько вам лет?
Введите ваш возраст (от {MIN_AGE} до {MAX_AGE} лет):
    """
    
    bot.send_message(message.chat.id, text)

def handle_age_input(bot, message, user_id):
    """Обработка ввода возраста"""
    try:
        age = int(message.text.strip())
    except ValueError:
        bot.send_message(message.chat.id, "❌ Пожалуйста, введите возраст числом:")
        return
    
    if age < MIN_AGE or age > MAX_AGE:
        bot.send_message(message.chat.id, f"❌ Возраст должен быть от {MIN_AGE} до {MAX_AGE} лет. Попробуйте еще раз:")
        return
    
    registration_data[user_id]['data']['age'] = age
    registration_data[user_id]['step'] = RegistrationState.WAITING_CITY
    
    text = """
✅ Возраст принят!

Шаг 3/5: В каком городе вы живете?
Введите название вашего города:
    """
    
    bot.send_message(message.chat.id, text)

def handle_city_input(bot, message, user_id):
    """Обработка ввода города"""
    city = message.text.strip()
    
    if not city:
        bot.send_message(message.chat.id, "❌ Город не может быть пустым. Попробуйте еще раз:")
        return
    
    if len(city) > MAX_CITY_LENGTH:
        bot.send_message(message.chat.id, f"❌ Название города слишком длинное (максимум {MAX_CITY_LENGTH} символов). Попробуйте еще раз:")
        return
    
    registration_data[user_id]['data']['city'] = city
    registration_data[user_id]['step'] = RegistrationState.WAITING_DESCRIPTION
    
    text = """
✅ Город записан!

Шаг 4/5: Расскажите о себе
Напишите краткое описание о себе, ваших интересах и том, кого вы ищете (до 500 символов):
    """
    
    bot.send_message(message.chat.id, text)

def handle_description_input(bot, message, user_id):
    """Обработка ввода описания"""
    description = message.text.strip()
    
    if not description:
        bot.send_message(message.chat.id, "❌ Описание не может быть пустым. Попробуйте еще раз:")
        return
    
    if len(description) > MAX_DESCRIPTION_LENGTH:
        bot.send_message(message.chat.id, f"❌ Описание слишком длинное (максимум {MAX_DESCRIPTION_LENGTH} символов). Попробуйте еще раз:")
        return
    
    registration_data[user_id]['data']['description'] = description
    registration_data[user_id]['step'] = RegistrationState.WAITING_PHOTO
    
    text = """
✅ Описание добавлено!

Шаг 5/5: Добавьте фото
Отправьте ваше фото или нажмите кнопку "Пропустить", если хотите добавить фото позже.
    """
    
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("⏭️ Пропустить фото", callback_data="skip_photo"))
    
    bot.send_message(message.chat.id, text, reply_markup=markup)

def handle_photo_input(bot, message, user_id, user_states):
    """Обработка загрузки фото"""
    photo_id = None
    
    if message.content_type == 'photo':
        # Берем фото наилучшего качества
        photo_id = message.photo[-1].file_id
    
    registration_data[user_id]['data']['photo_id'] = photo_id
    complete_registration(bot, message.chat.id, user_id, user_states)

def complete_registration(bot, chat_id, user_id, user_states):
    """Завершение регистрации"""
    data = registration_data[user_id]['data']
    username = None
    
    # Создаем пользователя в базе данных
    success = create_user(
        user_id=user_id,
        username=username,
        name=data['name'],
        age=data['age'],
        city=data['city'],
        description=data['description'],
        photo_id=data.get('photo_id')
    )
    
    if success:
        # Показываем созданную анкету
        profile_text = f"""
🎉 Анкета успешно создана!

👤 Ваша анкета:
📝 Имя: {data['name']}
🎂 Возраст: {data['age']}
🏙️ Город: {data['city']}
📖 О себе: {data['description']}
        """
        
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("👀 Начать просмотр анкет", callback_data="view_profiles"))
        markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
        
        if data.get('photo_id'):
            try:
                bot.send_photo(chat_id, data['photo_id'], caption=profile_text, reply_markup=markup)
            except:
                bot.send_message(chat_id, profile_text, reply_markup=markup)
        else:
            bot.send_message(chat_id, profile_text, reply_markup=markup)
        
        # Очищаем данные регистрации
        if user_id in registration_data:
            del registration_data[user_id]
        if user_id in user_states:
            del user_states[user_id]
    else:
        bot.send_message(chat_id, "❌ Произошла ошибка при создании анкеты. Попробуйте еще раз позже.")

def handle_skip_photo_callback(bot, call, user_states):
    """Обработка пропуска фото"""
    user_id = call.from_user.id

    if user_id in registration_data and registration_data[user_id]['step'] == RegistrationState.WAITING_PHOTO:
        registration_data[user_id]['data']['photo_id'] = None
        complete_registration(bot, call.message.chat.id, user_id, user_states)
        bot.answer_callback_query(call.id, "✅ Фото пропущено")
    else:
        bot.answer_callback_query(call.id, "❌ Ошибка: неверный этап регистрации")
