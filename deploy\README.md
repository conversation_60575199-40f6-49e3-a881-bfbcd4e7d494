# 🚀 Mini App для бота знакомств

## Быстрый деплой на Netlify

### Шаги:

1. **Зайдите на https://app.netlify.com/drop**
2. **Перетащите папку `deploy` в область загрузки**
3. **Получите HTTPS URL** (например: `https://amazing-app-123456.netlify.app`)
4. **Обновите URL в боте:**

```python
# В файле main.py замените URL:
markup.add(types.InlineKeyboardButton(
    "📱 Открыть Mini App", 
    web_app=types.WebAppInfo(url="ВАШ_NETLIFY_URL")
))
```

5. **Перезапустите бота:**
```bash
python main.py
```

## Возможности Mini App

- 🔥 **Swipe-интерфейс** для просмотра анкет
- 💕 **Совпадения** с уведомлениями
- 👤 **Профиль пользователя**
- 📊 **Статистика активности**
- 📱 **Адаптивный дизайн**
- ⚡ **Быстрая работа**

## Файлы для деплоя

- `index.html` - Полное приложение (HTML + CSS + JS)
- `netlify.toml` - Конфигурация Netlify
- `README.md` - Инструкции

## После деплоя

1. Откройте полученный URL в браузере
2. Проверьте работу Mini App
3. Обновите URL в боте
4. Протестируйте в Telegram

---

**Mini App готов к работе!** 🎉
