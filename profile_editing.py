from telebot import types
from database import get_user, update_user_field
from config import MIN_AGE, MAX_AGE, MAX_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_CITY_LENGTH

# Состояния редактирования
class EditingState:
    CHOOSING_FIELD = "choosing_field"
    EDITING_NAME = "editing_name"
    EDITING_AGE = "editing_age"
    EDITING_CITY = "editing_city"
    EDITING_DESCRIPTION = "editing_description"
    EDITING_PHOTO = "editing_photo"

# Временное хранилище данных редактирования
editing_sessions = {}

def start_profile_editing(bot, chat_id, user_id, user_states):
    """Начать редактирование профиля"""
    user = get_user(user_id)
    if not user:
        bot.send_message(chat_id, "❌ У вас нет анкеты для редактирования.")
        return
    
    user_states[user_id] = "editing_profile"
    editing_sessions[user_id] = {
        'state': EditingState.CHOOSING_FIELD
    }
    
    show_edit_menu(bot, chat_id, user)

def show_edit_menu(bot, chat_id, user):
    """Показать меню редактирования"""
    edit_text = f"""
✏️ Редактирование анкеты

Текущие данные:
📝 Имя: {user[2]}
🎂 Возраст: {user[3]}
🏙️ Город: {user[4]}
📖 О себе: {user[5][:50]}{'...' if len(user[5]) > 50 else ''}
📷 Фото: {'Есть' if user[6] else 'Нет'}

Что хотите изменить?
    """
    
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("📝 Имя", callback_data="edit_name"),
        types.InlineKeyboardButton("🎂 Возраст", callback_data="edit_age")
    )
    markup.add(
        types.InlineKeyboardButton("🏙️ Город", callback_data="edit_city"),
        types.InlineKeyboardButton("📖 Описание", callback_data="edit_description")
    )
    markup.add(types.InlineKeyboardButton("📷 Фото", callback_data="edit_photo"))
    markup.add(types.InlineKeyboardButton("✅ Готово", callback_data="finish_editing"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    bot.send_message(chat_id, edit_text, reply_markup=markup)

def handle_edit_callback(bot, call, user_states):
    """Обработка callback'ов редактирования"""
    user_id = call.from_user.id
    chat_id = call.message.chat.id
    
    if call.data == "edit_name":
        start_field_editing(bot, chat_id, user_id, "name", "📝 Введите новое имя:")
    elif call.data == "edit_age":
        start_field_editing(bot, chat_id, user_id, "age", f"🎂 Введите новый возраст ({MIN_AGE}-{MAX_AGE} лет):")
    elif call.data == "edit_city":
        start_field_editing(bot, chat_id, user_id, "city", "🏙️ Введите новый город:")
    elif call.data == "edit_description":
        start_field_editing(bot, chat_id, user_id, "description", "📖 Введите новое описание:")
    elif call.data == "edit_photo":
        start_field_editing(bot, chat_id, user_id, "photo", "📷 Отправьте новое фото или нажмите 'Удалить фото':")
    elif call.data == "finish_editing":
        finish_editing(bot, chat_id, user_id, user_states)
    elif call.data == "cancel_edit":
        cancel_editing(bot, chat_id, user_id, user_states)
    elif call.data == "delete_photo":
        delete_photo(bot, call, user_states)

def start_field_editing(bot, chat_id, user_id, field, prompt):
    """Начать редактирование конкретного поля"""
    editing_sessions[user_id] = {
        'state': f"editing_{field}",
        'field': field
    }
    
    markup = types.InlineKeyboardMarkup()
    if field == "photo":
        markup.add(types.InlineKeyboardButton("🗑️ Удалить фото", callback_data="delete_photo"))
    markup.add(types.InlineKeyboardButton("❌ Отмена", callback_data="cancel_edit"))
    
    bot.send_message(chat_id, prompt, reply_markup=markup)

def handle_edit_message(bot, message, user_states):
    """Обработка сообщений при редактировании"""
    user_id = message.from_user.id
    chat_id = message.chat.id
    
    if user_id not in editing_sessions:
        return False
    
    session = editing_sessions[user_id]
    field = session.get('field')
    
    if field == "name":
        return handle_name_edit(bot, message, user_id)
    elif field == "age":
        return handle_age_edit(bot, message, user_id)
    elif field == "city":
        return handle_city_edit(bot, message, user_id)
    elif field == "description":
        return handle_description_edit(bot, message, user_id)
    elif field == "photo":
        return handle_photo_edit(bot, message, user_id)
    
    return False

def handle_name_edit(bot, message, user_id):
    """Обработка редактирования имени"""
    name = message.text.strip()
    
    if not name:
        bot.send_message(message.chat.id, "❌ Имя не может быть пустым. Попробуйте еще раз:")
        return True
    
    if len(name) > MAX_NAME_LENGTH:
        bot.send_message(message.chat.id, f"❌ Имя слишком длинное (максимум {MAX_NAME_LENGTH} символов):")
        return True
    
    update_user_field(user_id, "name", name)
    bot.send_message(message.chat.id, f"✅ Имя изменено на: {name}")
    
    user = get_user(user_id)
    show_edit_menu(bot, message.chat.id, user)
    return True

def handle_age_edit(bot, message, user_id):
    """Обработка редактирования возраста"""
    try:
        age = int(message.text.strip())
    except ValueError:
        bot.send_message(message.chat.id, "❌ Введите возраст числом:")
        return True
    
    if age < MIN_AGE or age > MAX_AGE:
        bot.send_message(message.chat.id, f"❌ Возраст должен быть от {MIN_AGE} до {MAX_AGE} лет:")
        return True
    
    update_user_field(user_id, "age", age)
    bot.send_message(message.chat.id, f"✅ Возраст изменен на: {age}")
    
    user = get_user(user_id)
    show_edit_menu(bot, message.chat.id, user)
    return True

def handle_city_edit(bot, message, user_id):
    """Обработка редактирования города"""
    city = message.text.strip()
    
    if not city:
        bot.send_message(message.chat.id, "❌ Город не может быть пустым:")
        return True
    
    if len(city) > MAX_CITY_LENGTH:
        bot.send_message(message.chat.id, f"❌ Название города слишком длинное (максимум {MAX_CITY_LENGTH} символов):")
        return True
    
    update_user_field(user_id, "city", city)
    bot.send_message(message.chat.id, f"✅ Город изменен на: {city}")
    
    user = get_user(user_id)
    show_edit_menu(bot, message.chat.id, user)
    return True

def handle_description_edit(bot, message, user_id):
    """Обработка редактирования описания"""
    description = message.text.strip()
    
    if not description:
        bot.send_message(message.chat.id, "❌ Описание не может быть пустым:")
        return True
    
    if len(description) > MAX_DESCRIPTION_LENGTH:
        bot.send_message(message.chat.id, f"❌ Описание слишком длинное (максимум {MAX_DESCRIPTION_LENGTH} символов):")
        return True
    
    update_user_field(user_id, "description", description)
    bot.send_message(message.chat.id, "✅ Описание изменено!")
    
    user = get_user(user_id)
    show_edit_menu(bot, message.chat.id, user)
    return True

def handle_photo_edit(bot, message, user_id):
    """Обработка редактирования фото"""
    if message.content_type == 'photo':
        photo_id = message.photo[-1].file_id
        update_user_field(user_id, "photo_id", photo_id)
        bot.send_message(message.chat.id, "✅ Фото обновлено!")
        
        user = get_user(user_id)
        show_edit_menu(bot, message.chat.id, user)
        return True
    else:
        bot.send_message(message.chat.id, "❌ Пожалуйста, отправьте фото:")
        return True

def delete_photo(bot, call, user_states):
    """Удалить фото профиля"""
    user_id = call.from_user.id
    update_user_field(user_id, "photo_id", None)
    bot.answer_callback_query(call.id, "✅ Фото удалено")
    
    user = get_user(user_id)
    show_edit_menu(bot, call.message.chat.id, user)

def cancel_editing(bot, chat_id, user_id, user_states):
    """Отменить редактирование"""
    if user_id in editing_sessions:
        del editing_sessions[user_id]
    if user_id in user_states:
        del user_states[user_id]

    bot.send_message(chat_id, "❌ Редактирование отменено")

    # Показываем главное меню
    from main import show_main_menu
    show_main_menu(chat_id)

def finish_editing(bot, chat_id, user_id, user_states):
    """Завершить редактирование"""
    if user_id in editing_sessions:
        del editing_sessions[user_id]
    if user_id in user_states:
        del user_states[user_id]

    bot.send_message(chat_id, "✅ Редактирование завершено!")

    # Показываем обновленный профиль
    user = get_user(user_id)
    show_updated_profile(bot, chat_id, user)

def show_updated_profile(bot, chat_id, user):
    """Показать обновленный профиль"""
    profile_text = f"""
✅ Ваша обновленная анкета:

📝 Имя: {user[2]}
🎂 Возраст: {user[3]}
🏙️ Город: {user[4]}
📖 О себе: {user[5]}
    """
    
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("👀 Смотреть анкеты", callback_data="view_profiles"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    if user[6]:  # Если есть фото
        try:
            bot.send_photo(chat_id, user[6], caption=profile_text, reply_markup=markup)
        except:
            bot.send_message(chat_id, profile_text, reply_markup=markup)
    else:
        bot.send_message(chat_id, profile_text, reply_markup=markup)
