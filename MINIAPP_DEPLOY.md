# 🚀 Автоматический деплой Mini App

## 📋 Быстрая настройка

### 1. **Настройка GitHub Secrets**

Добавьте эти секреты в ваш репозиторий:

1. Откройте: https://github.com/KampeLoL/znakomstva_bot/settings/secrets/actions
2. Нажмите **New repository secret**
3. Добавьте два секрета:

**Секрет 1:**
- Name: `NETLIFY_AUTH_TOKEN`
- Secret: `****************************************`

**Секрет 2:**
- Name: `NETLIFY_SITE_ID`
- Secret: `fluffy-crumble-05e0fb` (ваш Site ID)

### 2. **Загрузка файлов**

Загрузите эти файлы в ваш репозиторий:

```
znakomstva_bot/
├── .github/
│   └── workflows/
│       └── deploy-miniapp.yml  # GitHub Actions workflow
├── deploy/
│   └── index.html              # Mini App (уже есть)
├── main.py                     # Telegram bot (уже есть)
└── MINIAPP_DEPLOY.md          # Эта инструкция
```

### 3. **Активация**

1. Загрузите файл `.github/workflows/deploy-miniapp.yml`
2. Настройте секреты (шаг 1)
3. Сделайте любое изменение в папке `deploy/`
4. Commit и push
5. Проверьте Actions: https://github.com/KampeLoL/znakomstva_bot/actions

## 🔄 Как это работает

### Автоматический деплой запускается когда:
- ✅ Push в main ветку
- ✅ Изменения в папке `deploy/`
- ✅ Изменения в workflow файле
- ✅ Ручной запуск через GitHub Actions

### Что происходит:
1. 📥 Скачивается код
2. 🧪 Проверяется HTML структура
3. 🚀 Деплоится на Netlify
4. 📊 Отправляются уведомления
5. 🔗 Обновляется Mini App

## 🎯 Тестирование

### После первого деплоя:
1. Откройте: https://fluffy-crumble-05e0fb.netlify.app
2. Проверьте работу Mini App
3. Протестируйте в Telegram боте
4. Убедитесь, что URL в main.py правильный

### Обновление Mini App:
1. Измените `deploy/index.html`
2. Commit и push
3. Проверьте Actions
4. Mini App обновится автоматически

## 🔧 Управление

### Ручной запуск деплоя:
1. https://github.com/KampeLoL/znakomstva_bot/actions
2. Выберите "🚀 Deploy Mini App to Netlify"
3. "Run workflow" → "Run workflow"

### Просмотр логов:
1. Actions → последний workflow
2. Кликните на job "📱 Build and Deploy Mini App"
3. Просмотрите детальные логи

### Откат к предыдущей версии:
1. Найдите нужный commit в истории
2. Revert изменения
3. Push - автоматически задеплоится старая версия

## 🚨 Решение проблем

### "NETLIFY_AUTH_TOKEN not found":
- Проверьте правильность названия секрета
- Убедитесь, что токен добавлен

### "NETLIFY_SITE_ID not found":
- Проверьте Site ID: fluffy-crumble-05e0fb
- Убедитесь, что секрет добавлен

### Деплой не запускается:
- Проверьте путь к файлу: `.github/workflows/deploy-miniapp.yml`
- Убедитесь, что изменения в папке `deploy/`

### HTML ошибки:
- Проверьте валидность HTML в `deploy/index.html`
- Убедитесь, что есть `<!DOCTYPE html>`

## 📱 Текущий статус

- **Репозиторий**: https://github.com/KampeLoL/znakomstva_bot
- **Mini App URL**: https://fluffy-crumble-05e0fb.netlify.app
- **Telegram Bot**: main.py
- **Actions**: https://github.com/KampeLoL/znakomstva_bot/actions

## 🎉 Результат

После настройки:
- 🤖 Изменили Mini App → автоматический деплой
- 📱 Всегда актуальная версия в Telegram
- 🔧 Простое управление через Git
- 📊 Мониторинг всех деплоев
- 🚀 Профессиональный workflow

---

**Автоматический деплой готов к работе!** 🚀
