# 📱 Mini App для бота знакомств

Современное веб-приложение для знакомств, интегрированное с Telegram Bot.

## 🎯 Возможности Mini App

### ✨ **Основные функции:**
- 🔥 **Swipe-интерфейс** - просмотр анкет как в Tinder
- 👤 **Профиль пользователя** - просмотр и редактирование анкеты
- 💕 **Совпадения** - список взаимных лайков
- 📊 **Статистика** - детальная аналитика активности

### 🎨 **Дизайн:**
- 📱 Адаптивный мобильный интерфейс
- 🌈 Современные градиенты и анимации
- ⚡ Плавные переходы и жесты
- 🎭 Темная/светлая тема (автоматически)

## 🏗️ Архитектура

### **Frontend (webapp/):**
- `index.html` - Основная структура приложения
- `styles.css` - Стили и анимации
- `app.js` - JavaScript логика и API интеграция

### **Backend:**
- `webapp_server.py` - Flask сервер с API
- Интеграция с существующей базой данных бота
- Проверка подлинности через Telegram Web App API

## 🚀 Запуск Mini App

### 1. **Установка зависимостей:**
```bash
pip install Flask==3.0.0 Flask-CORS==4.0.0
```

### 2. **Запуск веб-сервера:**
```bash
python webapp_server.py
```

### 3. **Доступ к приложению:**
- Локально: http://localhost:5000
- Через бота: кнопка "📱 Открыть Mini App"

## 🔧 API Endpoints

### **GET /api/user**
Получение профиля текущего пользователя
```json
{
  "id": 123,
  "name": "Иван",
  "age": 25,
  "city": "Москва",
  "description": "О себе...",
  "photo": "photo_id"
}
```

### **GET /api/profiles**
Получение анкет для просмотра
```json
{
  "profiles": [
    {
      "id": 456,
      "name": "Анна",
      "age": 23,
      "city": "СПб",
      "description": "Описание...",
      "photo": null
    }
  ]
}
```

### **POST /api/reaction**
Отправка реакции (лайк/дизлайк)
```json
{
  "target_user_id": 456,
  "reaction_type": "like"
}
```

### **GET /api/matches**
Получение совпадений
```json
{
  "matches": [
    {
      "id": 789,
      "name": "Мария",
      "age": 24,
      "city": "Казань"
    }
  ]
}
```

### **GET /api/stats**
Получение статистики пользователя
```json
{
  "likes_sent": 15,
  "likes_received": 8,
  "matches_count": 3,
  "dislikes_sent": 12
}
```

### **PUT /api/profile**
Обновление профиля
```json
{
  "name": "Новое имя",
  "age": 26,
  "city": "Новый город",
  "description": "Новое описание"
}
```

## 🔐 Безопасность

### **Telegram Web App Authentication:**
- Проверка подписи `initData` от Telegram
- Извлечение данных пользователя из `initDataUnsafe`
- Защита от подделки запросов

### **Заголовки запросов:**
```javascript
headers: {
  'X-Telegram-Init-Data': tg.initData,
  'Content-Type': 'application/json'
}
```

## 📱 Интеграция с ботом

### **Добавление кнопки Mini App в бота:**
```python
markup.add(types.InlineKeyboardButton(
    "📱 Открыть Mini App", 
    web_app=types.WebAppInfo(url="https://your-domain.com")
))
```

### **Настройка домена в BotFather:**
1. Отправьте `/setmenubutton` в @BotFather
2. Выберите вашего бота
3. Укажите URL Mini App

## 🎯 Особенности интерфейса

### **Swipe-жесты:**
- Свайп вправо = лайк ❤️
- Свайп влево = дизлайк 👎
- Кнопки для альтернативного управления

### **Навигация:**
- Нижняя панель с 4 разделами
- Плавные переходы между экранами
- Индикация активного раздела

### **Анимации:**
- Карточки с 3D-эффектами
- Плавное появление/исчезновение
- Обратная связь на действия пользователя

## 🚀 Деплой в продакшн

### **Требования:**
- HTTPS обязательно для Telegram Mini Apps
- Домен должен быть зарегистрирован в BotFather
- Сертификат SSL

### **Рекомендуемые платформы:**
- Heroku
- Vercel
- Railway
- DigitalOcean

## 🔄 Синхронизация с ботом

Mini App полностью интегрирован с основным ботом:
- Общая база данных
- Синхронизация реакций и совпадений
- Единая система уведомлений

## 📊 Мониторинг

Логирование всех действий:
- API запросы
- Ошибки аутентификации
- Действия пользователей
- Производительность

---

**Mini App готов к использованию!** 🎉

Современный интерфейс значительно улучшает пользовательский опыт по сравнению с обычными кнопками бота.
