#!/usr/bin/env python3
"""
Максимально надежный Playwright скрипт для загрузки workflow
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def robust_github_upload():
    """Максимально надежная загрузка с множественными проверками"""
    
    workflow_path = '.github/workflows/deploy-miniapp.yml'
    if not os.path.exists(workflow_path):
        print("❌ Файл workflow не найден!")
        return False
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_content = f.read()
    
    print("🎭 Запускаю надежный Playwright...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=2000,  # Очень медленно для надежности
            args=['--start-maximized', '--disable-blink-features=AutomationControlled']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        page = await context.new_page()
        
        try:
            print("🔐 Вход в GitHub...")
            await page.goto("https://github.com/login")
            await page.wait_for_load_state('domcontentloaded')
            
            # Вход
            await page.fill('#login_field', '<EMAIL>')
            await page.fill('#password', 'Mum007Mum!')
            await page.click('input[type="submit"]')
            
            # Ждем вход или 2FA
            await page.wait_for_timeout(3000)
            
            # Проверяем 2FA
            if 'two-factor' in page.url or await page.locator('input[name="otp"]').count() > 0:
                print("🔐 Введите 2FA код в браузере...")
                await page.wait_for_function("!window.location.href.includes('two-factor')", timeout=120000)
            
            print("✅ Вход выполнен!")
            
            # Переходим к репозиторию
            print("🌐 Переход к репозиторию...")
            await page.goto("https://github.com/KampeLoL/znakomstva_bot")
            await page.wait_for_load_state('networkidle')
            
            print("📁 Создание файла...")
            
            # Метод 1: Прямой URL
            print("🔄 Пробую метод 1: прямой URL...")
            await page.goto("https://github.com/KampeLoL/znakomstva_bot/new/main")
            await page.wait_for_timeout(3000)
            
            # Проверяем, есть ли поле filename
            if await page.locator('input[name="filename"]').count() == 0:
                print("🔄 Пробую метод 2: через интерфейс...")
                
                await page.goto("https://github.com/KampeLoL/znakomstva_bot")
                await page.wait_for_load_state('networkidle')
                
                # Ищем кнопку Add file разными способами
                selectors_to_try = [
                    'button:has-text("Add file")',
                    'summary:has-text("Add file")',
                    '[data-testid="add-file-button"]',
                    '.js-add-file-button',
                    'text=Add file'
                ]
                
                clicked = False
                for selector in selectors_to_try:
                    try:
                        if await page.locator(selector).count() > 0:
                            await page.locator(selector).click()
                            await page.wait_for_timeout(1000)
                            
                            # Ищем "Create new file"
                            if await page.locator('text=Create new file').count() > 0:
                                await page.locator('text=Create new file').click()
                                clicked = True
                                break
                    except:
                        continue
                
                if not clicked:
                    print("🔄 Пробую метод 3: альтернативный URL...")
                    await page.goto("https://github.com/KampeLoL/znakomstva_bot/upload/main")
                    await page.wait_for_timeout(2000)
                    
                    if await page.locator('text=create a new file').count() > 0:
                        await page.locator('text=create a new file').click()
            
            # Ждем появления формы
            print("📝 Ожидание формы создания файла...")
            
            # Пробуем разные селекторы для поля имени файла
            filename_selectors = [
                'input[name="filename"]',
                'input[placeholder*="Name your file"]',
                '#file-name-input',
                '.js-blob-filename'
            ]
            
            filename_input = None
            for selector in filename_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    filename_input = selector
                    break
                except:
                    continue
            
            if not filename_input:
                print("❌ Не удалось найти поле для имени файла")
                await page.screenshot(path='debug_filename.png')
                return False
            
            # Заполняем имя файла
            await page.fill(filename_input, '.github/workflows/deploy-miniapp.yml')
            print("✅ Имя файла заполнено")
            
            # Ждем появления редактора
            await page.wait_for_timeout(2000)
            
            # Заполняем содержимое
            print("📄 Заполнение содержимого...")
            
            content_filled = False
            
            # Способ 1: textarea
            if await page.locator('textarea[name="value"]').count() > 0:
                await page.fill('textarea[name="value"]', workflow_content)
                content_filled = True
                print("✅ Заполнено через textarea")
            
            # Способ 2: CodeMirror
            elif await page.locator('.CodeMirror').count() > 0:
                await page.evaluate(f"""
                    const cm = document.querySelector('.CodeMirror');
                    if (cm && cm.CodeMirror) {{
                        cm.CodeMirror.setValue({repr(workflow_content)});
                    }}
                """)
                content_filled = True
                print("✅ Заполнено через CodeMirror")
            
            # Способ 3: Клавиатура
            else:
                print("🔄 Использую клавиатуру...")
                await page.keyboard.press('Tab')
                await page.keyboard.press('Control+a')
                await page.keyboard.type(workflow_content)
                content_filled = True
                print("✅ Заполнено через клавиатуру")
            
            if not content_filled:
                print("❌ Не удалось заполнить содержимое")
                return False
            
            # Commit message
            print("💬 Заполнение commit message...")
            commit_selectors = [
                'input[name="commit_title"]',
                '#commit-summary-input',
                'input[placeholder*="commit"]'
            ]
            
            for selector in commit_selectors:
                if await page.locator(selector).count() > 0:
                    await page.fill(selector, "Add GitHub Actions workflow for Mini App auto-deploy")
                    break
            
            # Сохранение
            print("💾 Сохранение файла...")
            
            commit_selectors = [
                'button:has-text("Commit new file")',
                'input[value*="Commit new file"]',
                'button[type="submit"]:has-text("Commit")',
                'text=Commit new file'
            ]
            
            for selector in commit_selectors:
                if await page.locator(selector).count() > 0:
                    await page.locator(selector).click()
                    break
            
            # Ждем результата
            await page.wait_for_load_state('networkidle', timeout=20000)
            
            # Проверяем успех
            if 'deploy-miniapp.yml' in page.url or 'znakomstva_bot' in page.url:
                print("✅ Файл создан успешно!")
                
                # Переходим к Actions
                await page.goto("https://github.com/KampeLoL/znakomstva_bot/actions")
                await page.wait_for_load_state('networkidle')
                
                print("🎉 УСПЕХ! GitHub Actions настроен!")
                return True
            else:
                print("⚠️ Проверьте результат вручную")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            await page.screenshot(path='error_debug.png')
            print("📸 Скриншот: error_debug.png")
            return False
        
        finally:
            print("\n🔍 Проверьте результат в браузере")
            print("⏳ Нажмите Enter для закрытия...")
            input()
            await browser.close()

async def main():
    print("🎭 НАДЕЖНЫЙ PLAYWRIGHT ЗАГРУЗЧИК")
    print("=" * 40)
    
    choice = input("❓ Запустить? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        success = await robust_github_upload()
        
        if success:
            print("\n🎉 ВСЁ ГОТОВО!")
            print("✅ Автоматический деплой настроен")
            print("🧪 Теперь можете тестировать:")
            print("1. Измените deploy/index.html")
            print("2. Commit и push")
            print("3. Проверьте Actions")
        else:
            print("\n❌ Попробуйте еще раз или загрузите вручную")

if __name__ == "__main__":
    asyncio.run(main())
