name: 🚀 Deploy Mini App to Netlify

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  deploy:
    name: 📱 Build and Deploy
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: 📦 Install dependencies
      run: |
        npm install -g netlify-cli
        
    - name: 🏗️ Build project
      run: |
        echo "✅ Building Mini App..."
        # Здесь можно добавить команды сборки если нужно
        # npm run build
        ls -la
        
    - name: 🧪 Test HTML validity
      run: |
        echo "🧪 Testing HTML..."
        if [ -f "index.html" ]; then
          echo "✅ index.html found"
          # Проверяем базовую структуру HTML
          if grep -q "<!DOCTYPE html>" index.html; then
            echo "✅ Valid HTML structure"
          else
            echo "❌ Invalid HTML structure"
            exit 1
          fi
        else
          echo "❌ index.html not found"
          exit 1
        fi
        
    - name: 🚀 Deploy to Netlify
      uses: nwtgck/actions-netlify@v3.0
      with:
        publish-dir: '.'
        production-branch: main
        github-token: ${{ secrets.GITHUB_TOKEN }}
        deploy-message: "🚀 Deploy from GitHub Actions"
        enable-pull-request-comment: true
        enable-commit-comment: true
        overwrites-pull-request-comment: true
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        
    - name: 📊 Deploy Status
      run: |
        echo "🎉 Deployment completed!"
        echo "📱 Mini App URL: https://${{ secrets.NETLIFY_SITE_ID }}.netlify.app"
        echo "🤖 Update this URL in your Telegram bot"
        
    - name: 🔔 Notify on success
      if: success()
      run: |
        echo "✅ SUCCESS: Mini App deployed successfully!"
        echo "🔗 URL: https://${{ secrets.NETLIFY_SITE_ID }}.netlify.app"
        
    - name: 🚨 Notify on failure
      if: failure()
      run: |
        echo "❌ FAILED: Deployment failed!"
        echo "📋 Check the logs above for details"
