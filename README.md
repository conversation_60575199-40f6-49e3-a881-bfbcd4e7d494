# Бот для знакомств в Telegram

Простой и удобный бот для знакомств, написанный на Python с использованием библиотеки pyTelegramBotAPI.

## Возможности

### 🎯 Основные функции
- 📝 Создание анкеты с фото
- 👀 Просмотр анкет других пользователей
- ❤️ Система лайков и дизлайков
- 💕 Уведомления о взаимных симпатиях
- ✏️ Редактирование профиля в любое время
- 📊 Детальная статистика активности

### 🔧 Дополнительные возможности
- 🚫 Защита от самолайков
- 📈 Статистика для администраторов
- 🎲 Случайный порядок показа анкет
- 💾 Надежное хранение данных в SQLite
- 🔄 Обработка ошибок и валидация данных

## Установка и настройка

### 1. Клонирование репозитория
```bash
git clone <your-repo-url>
cd bot_znakomstv
```

### 2. Установка зависимостей
```bash
pip install -r requirements.txt
```

### 3. Создание бота в Telegram

1. Найдите @BotFather в Telegram
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Скопируйте полученный токен

### 4. Настройка конфигурации

Отредактируйте файл `.env` и замените `YOUR_BOT_TOKEN_HERE` на токен вашего бота:

```
BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
```

### 5. Запуск бота

```bash
python main.py
```

## Структура проекта

- `main.py` - Основной файл бота с обработчиками команд
- `config.py` - Конфигурационные настройки
- `database.py` - Работа с базой данных SQLite
- `user_registration.py` - Модуль регистрации пользователей
- `profile_viewing.py` - Модуль просмотра анкет
- `profile_editing.py` - Модуль редактирования профилей
- `matches.py` - Модуль управления совпадениями
- `.env` - Файл с токеном бота (не включается в git)
- `requirements.txt` - Список зависимостей

## Использование

### Команды бота

- `/start` - Начать работу с ботом / создать анкету
- `/help` - Показать справку
- `/profile` - Посмотреть свою анкету
- `/stats` - Показать детальную статистику
- `/admin` - Команды администратора (только для админов)

### Процесс знакомства

1. Пользователь создает анкету с именем, возрастом, городом, описанием и фото
2. Просматривает анкеты других пользователей
3. Ставит лайки или дизлайки
4. При взаимной симпатии получает уведомление с контактом

## База данных

Бот использует SQLite базу данных с тремя основными таблицами:

- `users` - Информация о пользователях
- `reactions` - Лайки и дизлайки
- `matches` - Взаимные симпатии

## Безопасность

- Токен бота хранится в переменных окружения
- Валидация всех пользовательских данных
- Ограничения на размер и формат загружаемых фото

## Разработка

Для добавления новых функций:

1. Создайте новый модуль в отдельном файле
2. Импортируйте его в `main.py`
3. Добавьте обработчики команд и callback'ов
4. Обновите базу данных при необходимости

## Лицензия

MIT License
