#!/usr/bin/env python3
"""
Автоматическая загрузка workflow файла в GitHub с помощью Playwright
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def upload_workflow_to_github():
    """Автоматически загружает workflow файл в GitHub"""

    # Читаем содержимое workflow файла
    workflow_path = '.github/workflows/deploy-miniapp.yml'
    if not os.path.exists(workflow_path):
        print("❌ Файл workflow не найден!")
        return False

    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_content = f.read()

    print("🎭 Запускаю Playwright для автоматической загрузки...")

    async with async_playwright() as p:
        # Запускаем браузер
        browser = await p.chromium.launch(headless=False)  # headless=False чтобы видеть процесс
        context = await browser.new_context()
        page = await context.new_page()

        try:
            print("🔐 Вход в GitHub...")
            await page.goto("https://github.com/login")
            await page.wait_for_load_state('networkidle')

            # Заполняем логин
            username_input = await page.wait_for_selector('#login_field', timeout=10000)
            await username_input.fill('<EMAIL>')
            print("✅ Email введен")

            # Заполняем пароль
            password_input = await page.wait_for_selector('#password', timeout=5000)
            await password_input.fill('Mum007Mum!')
            print("✅ Пароль введен")

            # Нажимаем кнопку входа
            login_button = await page.wait_for_selector('input[type="submit"][value="Sign in"]', timeout=5000)
            await login_button.click()
            print("🔄 Выполняется вход...")

            # Ждем завершения входа
            await page.wait_for_load_state('networkidle')

            # Проверяем, нужна ли двухфакторная аутентификация
            current_url = page.url
            if 'two-factor' in current_url or '2fa' in current_url:
                print("🔐 Требуется двухфакторная аутентификация")
                print("📱 Введите код из приложения аутентификации")

                # Ждем ввода кода пользователем
                await page.wait_for_selector('input[name="otp"]', timeout=60000)
                print("⏳ Ожидание ввода кода 2FA...")

                # Ждем перенаправления после 2FA
                await page.wait_for_function(
                    "() => !window.location.href.includes('two-factor') && !window.location.href.includes('2fa')",
                    timeout=120000
                )

            print("✅ Успешный вход в GitHub!")

            print("🌐 Переходим к репозиторию...")
            await page.goto("https://github.com/KampeLoL/znakomstva_bot")
            
            # Ждем загрузки страницы
            await page.wait_for_load_state('networkidle')
            
            print("📁 Ищу кнопку создания файла...")
            
            # Пытаемся найти кнопку "Add file" или "Create new file"
            add_file_selectors = [
                'button:has-text("Add file")',
                'a:has-text("Add file")',
                '[data-testid="add-file-button"]',
                '.js-add-file-button',
                'button[aria-label*="Add file"]'
            ]
            
            add_file_button = None
            for selector in add_file_selectors:
                try:
                    add_file_button = await page.wait_for_selector(selector, timeout=5000)
                    if add_file_button:
                        print(f"✅ Найдена кнопка: {selector}")
                        break
                except:
                    continue
            
            if not add_file_button:
                print("⚠️ Кнопка 'Add file' не найдена автоматически")
                print("🔍 Попробую альтернативные способы...")
                
                # Альтернативный способ - через URL
                await page.goto("https://github.com/KampeLoL/znakomstva_bot/new/main")
                await page.wait_for_load_state('networkidle')
            else:
                # Кликаем на кнопку Add file
                await add_file_button.click()
                
                # Ищем "Create new file"
                create_new_selectors = [
                    'a:has-text("Create new file")',
                    'button:has-text("Create new file")',
                    '[data-testid="create-new-file"]'
                ]
                
                for selector in create_new_selectors:
                    try:
                        create_button = await page.wait_for_selector(selector, timeout=3000)
                        if create_button:
                            await create_button.click()
                            break
                    except:
                        continue
            
            print("📝 Заполняю форму создания файла...")
            
            # Ждем появления формы
            await page.wait_for_selector('input[name="filename"]', timeout=10000)
            
            # Заполняем имя файла
            filename_input = await page.query_selector('input[name="filename"]')
            if filename_input:
                await filename_input.fill('.github/workflows/deploy-miniapp.yml')
                print("✅ Имя файла заполнено")
            
            # Заполняем содержимое файла
            content_selectors = [
                '.CodeMirror textarea',
                'textarea[name="value"]',
                '.js-code-textarea',
                '#file-contents'
            ]
            
            content_filled = False
            for selector in content_selectors:
                try:
                    content_area = await page.wait_for_selector(selector, timeout=3000)
                    if content_area:
                        await content_area.fill(workflow_content)
                        print("✅ Содержимое файла заполнено")
                        content_filled = True
                        break
                except:
                    continue
            
            if not content_filled:
                print("⚠️ Не удалось найти поле для содержимого файла")
                print("🔍 Попробую CodeMirror...")
                
                # Специальная обработка для CodeMirror
                await page.evaluate(f"""
                    const editors = document.querySelectorAll('.CodeMirror');
                    if (editors.length > 0) {{
                        editors[0].CodeMirror.setValue(`{workflow_content.replace('`', '\\`')}`);
                    }}
                """)
                print("✅ Содержимое добавлено через CodeMirror")
            
            # Заполняем commit message
            commit_selectors = [
                'input[name="commit_title"]',
                '#commit-summary-input',
                'input[placeholder*="commit"]'
            ]
            
            for selector in commit_selectors:
                try:
                    commit_input = await page.wait_for_selector(selector, timeout=3000)
                    if commit_input:
                        await commit_input.fill("Add GitHub Actions workflow for Mini App auto-deploy")
                        print("✅ Commit message заполнен")
                        break
                except:
                    continue
            
            print("💾 Готов к сохранению файла...")
            print("🔍 Ищу кнопку Commit...")
            
            # Ищем кнопку Commit
            commit_selectors = [
                'button:has-text("Commit new file")',
                'input[value*="Commit"]',
                '.js-commit-button',
                'button[type="submit"]'
            ]
            
            commit_button = None
            for selector in commit_selectors:
                try:
                    commit_button = await page.wait_for_selector(selector, timeout=3000)
                    if commit_button:
                        print(f"✅ Найдена кнопка commit: {selector}")
                        break
                except:
                    continue
            
            if commit_button:
                print("🚀 Сохраняю файл...")
                await commit_button.click()
                
                # Ждем перенаправления
                await page.wait_for_load_state('networkidle')
                
                print("✅ Файл успешно загружен!")
                print("🔗 Проверяю результат...")
                
                # Проверяем, что файл создан
                current_url = page.url
                if 'znakomstva_bot' in current_url:
                    print("✅ Успешно! Файл загружен в репозиторий")
                    
                    # Открываем страницу Actions
                    await page.goto("https://github.com/KampeLoL/znakomstva_bot/actions")
                    await page.wait_for_load_state('networkidle')
                    
                    print("📊 Открыта страница GitHub Actions")
                    print("🎉 Автоматическая загрузка завершена!")
                    
                    return True
                else:
                    print("⚠️ Возможно, произошла ошибка при загрузке")
                    return False
            else:
                print("❌ Не удалось найти кнопку Commit")
                print("🔍 Текущая страница:", page.url)
                return False
                
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            print("🔍 Текущая страница:", page.url)
            return False
        
        finally:
            # Оставляем браузер открытым для проверки
            print("🔍 Браузер остается открытым для проверки результата")
            print("⏳ Нажмите Enter для закрытия браузера...")
            input()
            await browser.close()

async def main():
    print("🎭 АВТОМАТИЧЕСКАЯ ЗАГРУЗКА WORKFLOW В GITHUB")
    print("=" * 60)
    
    print("📋 Что будет сделано:")
    print("1. Открыть GitHub репозиторий")
    print("2. Создать новый файл .github/workflows/deploy-miniapp.yml")
    print("3. Заполнить содержимое")
    print("4. Сохранить файл")
    print("5. Открыть страницу Actions")
    
    choice = input("\n❓ Запустить автоматическую загрузку? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        success = await upload_workflow_to_github()
        
        if success:
            print("\n🎉 УСПЕХ!")
            print("=" * 60)
            print("✅ Workflow файл загружен в GitHub")
            print("✅ GitHub Actions активирован")
            print("✅ Автоматический деплой настроен")
            
            print("\n🧪 ТЕСТИРОВАНИЕ:")
            print("1. Измените deploy/index.html")
            print("2. Commit и push")
            print("3. Проверьте Actions - деплой запустится!")
            
            print("\n🔗 Полезные ссылки:")
            print("📊 Actions: https://github.com/KampeLoL/znakomstva_bot/actions")
            print("📱 Mini App: https://fluffy-crumble-05e0fb.netlify.app")
        else:
            print("\n❌ Что-то пошло не так")
            print("📋 Попробуйте загрузить файл вручную:")
            print("1. Откройте: https://github.com/KampeLoL/znakomstva_bot")
            print("2. Create new file")
            print("3. Имя: .github/workflows/deploy-miniapp.yml")
            print("4. Скопируйте содержимое из локального файла")
    else:
        print("\n📋 Ручная загрузка:")
        print("1. Откройте: https://github.com/KampeLoL/znakomstva_bot")
        print("2. Create new file")
        print("3. Имя: .github/workflows/deploy-miniapp.yml")
        print("4. Скопируйте содержимое из .github/workflows/deploy-miniapp.yml")

if __name__ == "__main__":
    asyncio.run(main())
