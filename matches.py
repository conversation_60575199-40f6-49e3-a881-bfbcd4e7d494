from telebot import types
from database import get_user_matches, get_user_likes_count

def show_user_matches(bot, chat_id, user_id):
    """Показать совпадения пользователя"""
    matches = get_user_matches(user_id)
    
    if not matches:
        no_matches_text = """
💔 У вас пока нет взаимных симпатий.

Продолжайте просматривать анкеты и ставить лайки - возможно, кто-то уже оценил вашу анкету!
        """
        
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("👀 Смотреть анкеты", callback_data="view_profiles"))
        markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
        
        bot.send_message(chat_id, no_matches_text, reply_markup=markup)
        return
    
    matches_text = f"❤️ Ваши взаимные симпатии ({len(matches)}):\n\n"
    
    for i, match in enumerate(matches, 1):
        matches_text += f"{i}. {match[2]}, {match[3]} лет, {match[4]}\n"
        if match[1]:  # username
            matches_text += f"   Контакт: @{match[1]}\n"
        matches_text += "\n"
    
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("👀 Смотреть анкеты", callback_data="view_profiles"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    bot.send_message(chat_id, matches_text, reply_markup=markup)

def show_user_stats(bot, chat_id, user_id):
    """Показать статистику пользователя"""
    likes_count = get_user_likes_count(user_id)
    matches_count = len(get_user_matches(user_id))
    
    stats_text = f"""
📊 Ваша статистика:

❤️ Лайков получено: {likes_count}
💕 Взаимных симпатий: {matches_count}

Продолжайте быть активными, чтобы найти больше интересных людей!
    """
    
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("👀 Смотреть анкеты", callback_data="view_profiles"))
    markup.add(types.InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu"))
    
    bot.send_message(chat_id, stats_text, reply_markup=markup)
