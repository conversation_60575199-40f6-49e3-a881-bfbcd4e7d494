import os
from dotenv import load_dotenv

# Загружаем переменные окружения из .env файла
load_dotenv()

# Токен бота (получить у @BotFather в Telegram)
BOT_TOKEN = os.getenv('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# Настройки базы данных
DATABASE_NAME = 'dating_bot.db'

# Настройки для анкет
MAX_PHOTO_SIZE = 10 * 1024 * 1024  # 10 MB
ALLOWED_PHOTO_FORMATS = ['jpg', 'jpeg', 'png']
MIN_AGE = 18
MAX_AGE = 100
MAX_NAME_LENGTH = 50
MAX_DESCRIPTION_LENGTH = 500
MAX_CITY_LENGTH = 50
