#!/usr/bin/env python3
"""
Финальная проверка готовности автоматического деплоя
"""

import os
import webbrowser

def check_files():
    """Проверяет наличие всех необходимых файлов"""
    files_to_check = [
        '.github/workflows/deploy-miniapp.yml',
        'deploy/index.html',
        'main.py'
    ]
    
    print("📁 Проверка файлов:")
    all_good = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - НЕ НАЙДЕН!")
            all_good = False
    
    return all_good

def show_next_steps():
    """Показывает следующие шаги"""
    print("\n🚀 СЛЕДУЮЩИЕ ШАГИ:")
    print("=" * 50)
    
    print("\n1. 📁 ЗАГРУЗИТЕ WORKFLOW ФАЙЛ:")
    print("   • Откройте: https://github.com/KampeLoL/znakomstva_bot")
    print("   • Create new file")
    print("   • Имя: .github/workflows/deploy-miniapp.yml")
    print("   • Скопируйте содержимое из локального файла")
    print("   • Commit new file")
    
    print("\n2. 🧪 ПРОТЕСТИРУЙТЕ ДЕПЛОЙ:")
    print("   • Измените deploy/index.html")
    print("   • Commit и push")
    print("   • Проверьте Actions")
    
    print("\n3. 📊 МОНИТОРИНГ:")
    print("   • Actions: https://github.com/KampeLoL/znakomstva_bot/actions")
    print("   • Mini App: https://fluffy-crumble-05e0fb.netlify.app")
    print("   • Secrets: https://github.com/KampeLoL/znakomstva_bot/settings/secrets/actions")

def open_required_pages():
    """Открывает необходимые страницы"""
    pages = [
        ("GitHub репозиторий", "https://github.com/KampeLoL/znakomstva_bot"),
        ("GitHub Actions", "https://github.com/KampeLoL/znakomstva_bot/actions"),
        ("Mini App", "https://fluffy-crumble-05e0fb.netlify.app")
    ]
    
    print("\n🌐 Открываю необходимые страницы...")
    for name, url in pages:
        print(f"   📖 {name}")
        webbrowser.open(url)

def main():
    print("🔍 ФИНАЛЬНАЯ ПРОВЕРКА АВТОМАТИЧЕСКОГО ДЕПЛОЯ")
    print("=" * 60)
    
    # Проверяем файлы
    if check_files():
        print("\n✅ Все файлы на месте!")
    else:
        print("\n❌ Некоторые файлы отсутствуют!")
        return
    
    print("\n📋 ТЕКУЩИЙ СТАТУС:")
    print("✅ Workflow файл создан")
    print("✅ Mini App готов")
    print("✅ Секреты добавлены в GitHub")
    print("✅ Netlify сайт работает")
    
    print("\n🎯 ЧТО НУЖНО СДЕЛАТЬ:")
    print("1. Загрузить workflow файл в GitHub")
    print("2. Протестировать автоматический деплой")
    
    choice = input("\n❓ Открыть необходимые страницы? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'да', 'д']:
        open_required_pages()
        show_next_steps()
        
        print("\n🎉 ГОТОВО К ЗАПУСКУ!")
        print("=" * 60)
        print("📝 Инструкция:")
        print("1. В открывшемся GitHub создайте файл .github/workflows/deploy-miniapp.yml")
        print("2. Скопируйте содержимое из локального файла")
        print("3. Сохраните (Commit new file)")
        print("4. Сделайте любое изменение в deploy/index.html")
        print("5. Commit и push")
        print("6. Проверьте Actions - деплой запустится автоматически!")
        
        print("\n🔗 После настройки:")
        print("   • Любое изменение в deploy/ → автоматический деплой")
        print("   • Mini App всегда актуален")
        print("   • Мониторинг через GitHub Actions")
        print("   • Уведомления о статусе")
        
    else:
        show_next_steps()
    
    print("\n" + "=" * 60)
    print("🚀 Автоматический деплой почти готов!")
    print("Осталось только загрузить workflow файл в GitHub!")

if __name__ == "__main__":
    main()
