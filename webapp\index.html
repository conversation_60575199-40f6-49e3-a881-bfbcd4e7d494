<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Знакомства - Mini App</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- Загрузочный экран -->
        <div id="loading" class="screen active">
            <div class="loading-spinner"></div>
            <h2>Загрузка...</h2>
        </div>

        <!-- Главный экран с навигацией -->
        <div id="main" class="screen">
            <!-- Навигационная панель -->
            <nav class="bottom-nav">
                <button class="nav-btn active" data-screen="discover">
                    <span class="nav-icon">🔥</span>
                    <span class="nav-label">Анкеты</span>
                </button>
                <button class="nav-btn" data-screen="matches">
                    <span class="nav-icon">💕</span>
                    <span class="nav-label">Совпадения</span>
                </button>
                <button class="nav-btn" data-screen="profile">
                    <span class="nav-icon">👤</span>
                    <span class="nav-label">Профиль</span>
                </button>
                <button class="nav-btn" data-screen="stats">
                    <span class="nav-icon">📊</span>
                    <span class="nav-label">Статистика</span>
                </button>
            </nav>

            <!-- Экран просмотра анкет -->
            <div id="discover" class="content-screen active">
                <div class="header">
                    <h1>🔥 Знакомства</h1>
                    <button id="settingsBtn" class="settings-btn">⚙️</button>
                </div>
                
                <div class="cards-container">
                    <div id="noMoreCards" class="no-more-cards hidden">
                        <h3>😔 Анкеты закончились</h3>
                        <p>Заходите позже или пригласите друзей!</p>
                        <button id="refreshBtn" class="refresh-btn">🔄 Обновить</button>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="dislikeBtn" class="action-btn dislike-btn">
                        <span class="btn-icon">👎</span>
                    </button>
                    <button id="likeBtn" class="action-btn like-btn">
                        <span class="btn-icon">❤️</span>
                    </button>
                </div>
            </div>

            <!-- Экран совпадений -->
            <div id="matches" class="content-screen">
                <div class="header">
                    <h1>💕 Совпадения</h1>
                </div>
                <div id="matchesList" class="matches-list">
                    <!-- Список совпадений будет загружен динамически -->
                </div>
            </div>

            <!-- Экран профиля -->
            <div id="profile" class="content-screen">
                <div class="header">
                    <h1>👤 Мой профиль</h1>
                    <button id="editProfileBtn" class="edit-btn">✏️</button>
                </div>
                <div id="profileContent" class="profile-content">
                    <!-- Содержимое профиля будет загружено динамически -->
                </div>
            </div>

            <!-- Экран статистики -->
            <div id="stats" class="content-screen">
                <div class="header">
                    <h1>📊 Статистика</h1>
                </div>
                <div id="statsContent" class="stats-content">
                    <!-- Статистика будет загружена динамически -->
                </div>
            </div>
        </div>

        <!-- Модальное окно редактирования профиля -->
        <div id="editModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>✏️ Редактировать профиль</h2>
                    <button id="closeModal" class="close-btn">✕</button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <div class="form-group">
                            <label for="editName">Имя:</label>
                            <input type="text" id="editName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="editAge">Возраст:</label>
                            <input type="number" id="editAge" name="age" min="18" max="100" required>
                        </div>
                        <div class="form-group">
                            <label for="editCity">Город:</label>
                            <input type="text" id="editCity" name="city" required>
                        </div>
                        <div class="form-group">
                            <label for="editDescription">О себе:</label>
                            <textarea id="editDescription" name="description" rows="4" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="editPhoto">Фото:</label>
                            <input type="file" id="editPhoto" name="photo" accept="image/*">
                        </div>
                        <div class="form-actions">
                            <button type="button" id="cancelEdit" class="cancel-btn">Отмена</button>
                            <button type="submit" class="save-btn">Сохранить</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="app_standalone.js"></script>
</body>
</html>
