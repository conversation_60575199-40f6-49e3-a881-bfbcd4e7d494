/* Основные стили */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
    user-select: none;
}

#app {
    height: 100vh;
    position: relative;
}

/* Экраны */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.screen.active {
    opacity: 1;
    visibility: visible;
}

/* Загрузочный экран */
#loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Главный экран */
#main {
    background: #f8f9fa;
    padding-bottom: 80px;
}

/* Заголовок */
.header {
    background: white;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header h1 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.settings-btn, .edit-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background 0.2s;
}

.settings-btn:hover, .edit-btn:hover {
    background: rgba(0,0,0,0.1);
}

/* Контентные экраны */
.content-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    transform: translateX(20px);
}

.content-screen.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* Контейнер карточек */
.cards-container {
    position: relative;
    height: calc(100vh - 200px);
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Карточка анкеты */
.profile-card {
    position: absolute;
    width: 90%;
    max-width: 350px;
    height: 70%;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
    cursor: grab;
    transition: transform 0.3s ease;
}

.profile-card:active {
    cursor: grabbing;
}

.profile-card.dragging {
    transition: none;
}

.card-image {
    width: 100%;
    height: 60%;
    object-fit: cover;
    background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
}

.card-content {
    padding: 20px;
    height: 40%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-name {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.card-info {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.card-description {
    font-size: 14px;
    color: #888;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* Кнопки действий */
.action-buttons {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 30px;
    z-index: 10;
}

.action-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.dislike-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.like-btn {
    background: linear-gradient(135deg, #51cf66, #40c057);
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn:active {
    transform: scale(0.95);
}

/* Нижняя навигация */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.nav-btn {
    background: none;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 10px;
    min-width: 60px;
}

.nav-btn.active {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-label {
    font-size: 12px;
    font-weight: 500;
}

/* Сообщение об отсутствии карточек */
.no-more-cards {
    text-align: center;
    padding: 40px 20px;
}

.no-more-cards.hidden {
    display: none;
}

.refresh-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 20px;
    transition: transform 0.2s ease;
}

.refresh-btn:hover {
    transform: scale(1.05);
}

/* Модальное окно */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
}

.modal-body {
    padding: 20px;
}

/* Формы */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 30px;
}

.cancel-btn, .save-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-btn {
    background: #f8f9fa;
    color: #666;
}

.save-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.cancel-btn:hover, .save-btn:hover {
    transform: translateY(-2px);
}

/* Списки */
.matches-list, .stats-content, .profile-content {
    padding: 20px;
}

.match-item {
    background: white;
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.match-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin-right: 15px;
}

.match-info h3 {
    margin-bottom: 5px;
    color: #333;
}

.match-info p {
    color: #666;
    font-size: 14px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* Адаптивность */
@media (max-width: 480px) {
    .cards-container {
        padding: 10px;
    }

    .profile-card {
        width: 95%;
    }

    .action-buttons {
        gap: 20px;
    }

    .action-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}
